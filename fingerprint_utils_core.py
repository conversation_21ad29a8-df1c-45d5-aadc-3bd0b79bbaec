"""
Optimized Core Utilities for Fingerprint Modification

This module contains the core functions for browser fingerprint modification,
including OS/browser detection, WebGL info generation, and cleanup routines.
"""
import os
import json
import psutil
import random
import shutil
import time
from fingerprint_core import config_dir

def clean_browser_files():
    """
    Clean browser cookies and cache files, preserving login-related cookies.
    
    Returns:
        tuple: (success, message)
    """
    try:
        # Check if any browsers are running
        browser_processes = ['chrome.exe', 'msedge.exe', 'firefox.exe', 'brave.exe', 'opera.exe']
        browser_running = any(
            proc.info['name'].lower() in browser_processes 
            for proc in psutil.process_iter(['name'])
        )
        
        if browser_running:
            return False, "Browsers are currently running. Please close all browsers before cleaning."
        
        cookie_files = ['Cookies', 'Cookies-journal', 'Network', 'IndexedDB', 'Local Storage', 
                     'Session Storage', 'WebStorage', 'QuotaManager', 'Cache', 'Code Cache', 
                     'GPUCache', 'Media Cache', 'Service Worker']
        
        preserve_cookies = ['login', 'auth', 'account', 'session', 'user', 'pass', 'credential', 'token']
        
        cleaned_items = []
        preserved_items = []
        
        # Clean sandbox directories
        sandbox_dir = os.path.join(config_dir, "chrome_sandbox")
        if os.path.exists(sandbox_dir):
            try:
                # Keep the most recent session
                dirs = [d for d in os.listdir(sandbox_dir) if os.path.isdir(os.path.join(sandbox_dir, d))]
                if dirs:
                    dirs.sort(key=lambda x: os.path.getmtime(os.path.join(sandbox_dir, x)))
                    
                    # Delete all but the most recent session
                    for old_dir in dirs[:-1]:
                        old_path = os.path.join(sandbox_dir, old_dir)
                        shutil.rmtree(old_path, ignore_errors=True)
                        cleaned_items.append(f"Cleaned old sandbox session: {old_dir}")
            except Exception as e:
                print(f"Error cleaning sandbox: {str(e)}")
        
        # Look for Chrome profile directories
        chrome_paths = [
            os.path.join(os.environ.get("LocalAppData", ""), "Google", "Chrome", "User Data"),
            os.path.join(os.environ.get("LocalAppData", ""), "Microsoft", "Edge", "User Data"),
            os.path.join(os.environ.get("AppData", ""), "Google", "Chrome", "User Data"),
            os.path.join(os.environ.get("LocalAppData", ""), "BraveSoftware", "Brave-Browser", "User Data")
        ]
        
        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                # Look for profile directories
                profiles = ['Default', 'Profile 1', 'Profile 2', 'Profile 3']
                for profile in profiles:
                    profile_path = os.path.join(chrome_path, profile)
                    if os.path.exists(profile_path):
                        # Clean each file/directory in the cookie_files list
                        for cookie_file in cookie_files:
                            cookie_path = os.path.join(profile_path, cookie_file)
                            if os.path.exists(cookie_path):
                                # Skip files that contain preserve keywords
                                should_preserve = False
                                if os.path.isfile(cookie_path):
                                    try:
                                        with open(cookie_path, 'rb') as f:
                                            content = f.read(1024).lower()  # Read just the beginning
                                            for keyword in preserve_cookies:
                                                if keyword.encode('utf-8') in content:
                                                    should_preserve = True
                                                    preserved_items.append(f"{profile}/{cookie_file}")
                                                    break
                                    except:
                                        pass
                                
                                if not should_preserve:
                                    try:
                                        if os.path.isfile(cookie_path):
                                            # For files, create an empty file instead of deleting
                                            open(cookie_path, 'w').close()
                                        elif os.path.isdir(cookie_path):
                                            # For directories, keep the directory but remove contents
                                            for item in os.listdir(cookie_path):
                                                item_path = os.path.join(cookie_path, item)
                                                try:
                                                    if os.path.isfile(item_path):
                                                        os.remove(item_path)
                                                    elif os.path.isdir(item_path):
                                                        shutil.rmtree(item_path)
                                                except:
                                                    pass
                                        
                                        cleaned_items.append(f"{profile}/{cookie_file}")
                                    except:
                                        pass
        
        if cleaned_items:
            preserved_msg = f" ({len(preserved_items)} login-related items preserved)" if preserved_items else ""
            return (True, f"Successfully cleaned {len(cleaned_items)} cookie and cache files{preserved_msg}")
        else:
            return (True, "No files found that need cleaning")
    except Exception as e:
        return False, f"Error cleaning cookies and cache: {str(e)}"

def get_os_info_from_ua(user_agent):
    """
    Extract OS information from a User-Agent string.
    
    Args:
        user_agent (str): Browser User-Agent string
        
    Returns:
        dict: OS information including platform, version, architecture
    """
    os_info = {
        'platform': 'Windows',
        'oscpu': 'Windows NT 10.0',
        'platform_version': '10.0',
        'architecture': 'x86',
        'bitness': '64'
    }
    
    # Windows detection
    if 'Windows' in user_agent:
        if 'Windows NT 10' in user_agent:
            os_info['platform'] = 'Windows'
            os_info['oscpu'] = 'Windows NT 10.0'
            os_info['platform_version'] = '10.0'
        elif 'Windows NT 6.3' in user_agent:
            os_info['platform'] = 'Windows'
            os_info['oscpu'] = 'Windows NT 6.3'
            os_info['platform_version'] = '6.3'
        elif 'Windows NT 6.2' in user_agent:
            os_info['platform'] = 'Windows'
            os_info['oscpu'] = 'Windows NT 6.2'
            os_info['platform_version'] = '6.2'
        elif 'Windows NT 6.1' in user_agent:
            os_info['platform'] = 'Windows'
            os_info['oscpu'] = 'Windows NT 6.1'
            os_info['platform_version'] = '6.1'
    
    # macOS detection
    elif 'Macintosh' in user_agent or 'Mac OS X' in user_agent:
        os_info['platform'] = 'MacIntel'
        if 'Mac OS X 10_15' in user_agent:
            os_info['oscpu'] = 'Intel Mac OS X 10_15'
            os_info['platform_version'] = '10.15'
        elif 'Mac OS X 10_14' in user_agent:
            os_info['oscpu'] = 'Intel Mac OS X 10_14'
            os_info['platform_version'] = '10.14'
        else:
            os_info['oscpu'] = 'Intel Mac OS X 10_15'
            os_info['platform_version'] = '10.15'
    
    # Linux detection
    elif 'Linux' in user_agent:
        if 'x86_64' in user_agent:
            os_info['platform'] = 'Linux x86_64'
            os_info['oscpu'] = 'Linux x86_64'
        else:
            os_info['platform'] = 'Linux'
            os_info['oscpu'] = 'Linux'
        os_info['platform_version'] = ''
    
    # iOS detection
    elif 'iPhone' in user_agent or 'iPad' in user_agent:
        os_info['platform'] = 'iPhone' if 'iPhone' in user_agent else 'iPad'
        if 'OS 17_' in user_agent:
            os_info['oscpu'] = 'CPU iPhone OS 17_0 like Mac OS X'
            os_info['platform_version'] = '17.0'
        elif 'OS 16_' in user_agent:
            os_info['oscpu'] = 'CPU iPhone OS 16_0 like Mac OS X'
            os_info['platform_version'] = '16.0'
        else:
            os_info['oscpu'] = 'CPU iPhone OS 15_0 like Mac OS X'
            os_info['platform_version'] = '15.0'
        os_info['architecture'] = 'arm'
    
    # Android detection
    elif 'Android' in user_agent:
        os_info['platform'] = 'Android'
        if 'Android 13' in user_agent:
            os_info['oscpu'] = 'Linux; Android 13'
            os_info['platform_version'] = '13'
        elif 'Android 12' in user_agent:
            os_info['oscpu'] = 'Linux; Android 12'
            os_info['platform_version'] = '12'
        else:
            os_info['oscpu'] = 'Linux; Android 11'
            os_info['platform_version'] = '11'
        os_info['architecture'] = 'arm'
    
    # Architecture and bitness detection
    if 'Win64' in user_agent or 'x86_64' in user_agent or 'x64' in user_agent:
        os_info['architecture'] = 'x86'
        os_info['bitness'] = '64'
    elif 'arm' in user_agent.lower():
        os_info['architecture'] = 'arm'
        if 'arm64' in user_agent.lower():
            os_info['bitness'] = '64'
        else:
            os_info['bitness'] = '32'
    else:
        os_info['architecture'] = 'x86'
        os_info['bitness'] = '32'
    
    return os_info

def get_browser_info_from_ua(user_agent):
    """
    Extract browser information from a User-Agent string.
    
    Args:
        user_agent (str): Browser User-Agent string
        
    Returns:
        dict: Browser information including vendor, product, version
    """
    browser_info = {
        'app_name': 'Netscape',
        'app_version': '5.0',
        'vendor': 'Google Inc.',
        'product': 'Gecko',
        'product_sub': '20030107',
        'full_version': '120.0.0.0',
        'build_id': '20231107',
        'brands_json': '[{"brand":"Chromium","version":"120"},{"brand":"Google Chrome","version":"120"},{"brand":"Not=A?Brand","version":"24"}]'
    }
    
    # Chrome detection
    if 'Chrome/' in user_agent:
        browser_info['vendor'] = 'Google Inc.'
        chrome_version = user_agent.split('Chrome/')[1].split(' ')[0]
        browser_info['full_version'] = chrome_version
        major_version = chrome_version.split('.')[0]
        
        # Edge detection
        if 'Edg/' in user_agent:
            browser_info['vendor'] = 'Microsoft'
            edge_version = user_agent.split('Edg/')[1].split(' ')[0]
            browser_info['full_version'] = edge_version
            major_version = edge_version.split('.')[0]
            
            brands = [
                {"brand":"Microsoft Edge","version":major_version},
                {"brand":"Chromium","version":major_version},
                {"brand":"Not=A?Brand","version":"24"}
            ]
            browser_info['brands_json'] = json.dumps(brands)
        
        # Samsung Browser detection
        elif 'SamsungBrowser/' in user_agent:
            browser_info['vendor'] = 'Samsung'
            samsung_version = user_agent.split('SamsungBrowser/')[1].split(' ')[0]
            browser_info['full_version'] = samsung_version
            
            brands = [
                {"brand":"Samsung Browser","version":samsung_version.split('.')[0]},
                {"brand":"Chromium","version":major_version},
                {"brand":"Not=A?Brand","version":"24"}
            ]
            browser_info['brands_json'] = json.dumps(brands)
        
        # Standard Chrome
        else:
            brands = [
                {"brand":"Chromium","version":major_version},
                {"brand":"Google Chrome","version":major_version},
                {"brand":"Not=A?Brand","version":"24"}
            ]
            browser_info['brands_json'] = json.dumps(brands)
    
    # Firefox detection
    elif 'Firefox/' in user_agent:
        browser_info['vendor'] = ''
        browser_info['product'] = 'Gecko'
        browser_info['product_sub'] = '20100101'
        firefox_version = user_agent.split('Firefox/')[1].split(' ')[0]
        browser_info['full_version'] = firefox_version
        browser_info['brands_json'] = '[]'
    
    # Safari detection
    elif 'Safari/' in user_agent and 'Chrome/' not in user_agent:
        browser_info['vendor'] = 'Apple Computer, Inc.'
        browser_info['product'] = 'Gecko'
        safari_version = user_agent.split('Safari/')[1].split(' ')[0] if 'Safari/' in user_agent else '605.1.15'
        
        if 'Version/' in user_agent:
            version = user_agent.split('Version/')[1].split(' ')[0]
            browser_info['full_version'] = version
        else:
            browser_info['full_version'] = '17.0'
            
        browser_info['brands_json'] = '[]'
    
    return browser_info

def generate_consistent_webgl_info(os_info, profile=None):
    """
    Generate consistent WebGL information based on OS and device configuration.
    
    Args:
        os_info (dict): OS information from get_os_info_from_ua()
        profile (dict, optional): Device profile with WebGL settings
        
    Returns:
        dict: WebGL information including vendor, renderer, extensions
    """
    # If profile is provided, use its WebGL info
    if profile and 'webgl_vendor' in profile and 'webgl_renderer' in profile:
        webgl_info = {}
        
        # Use vendor and renderer from profile
        webgl_info['vendor'] = profile['webgl_vendor']
        webgl_info['renderer'] = profile['webgl_renderer']
        
        # Use WebGL extensions from profile
        if 'webgl_extensions' in profile:
            webgl_info['extensions_json'] = json.dumps(profile['webgl_extensions'])
            # For WebGL2 extensions, use specific ones if provided, otherwise use WebGL1 extensions
            webgl_info['extensions2_json'] = json.dumps(profile.get('webgl2_extensions', profile['webgl_extensions']))
        else:
            # If no extensions provided, generate standard lists
            webgl1_extensions = [
                "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
                "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
                "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
                "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
                "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
            ]
            webgl2_extensions = [
                "EXT_color_buffer_float", "EXT_disjoint_timer_query_webgl2",
                "EXT_float_blend", "EXT_texture_filter_anisotropic",
                "WEBGL_compressed_texture_s3tc", "WEBGL_debug_renderer_info", "WEBGL_lose_context"
            ]
            
            webgl_info['extensions_json'] = json.dumps(webgl1_extensions)
            webgl_info['extensions2_json'] = json.dumps(webgl2_extensions)
        
        # Set standard precision format parameters
        webgl_info['precision'] = profile.get('webgl_precision', 23)
        webgl_info['range_min'] = profile.get('webgl_range_min', 127)
        webgl_info['range_max'] = profile.get('webgl_range_max', 127)
        
        return webgl_info
    
    # If no profile, generate based on OS
    webgl_info = {}
    
    # Choose appropriate GPU info based on OS
    if os_info['platform'].startswith('Windows'):
        # Windows GPU info
        vendors = ['Google Inc.', 'Google Inc. (NVIDIA)', 'Google Inc. (Intel)']
        renderers = [
            'ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)',
            'ANGLE (NVIDIA, NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0, D3D11)'
        ]
    elif os_info['platform'] == 'MacIntel':
        # Mac GPU info
        vendors = ['Apple Inc.', 'Apple GPU']
        renderers = [
            'Apple M1',
            'Apple M1 Pro',
            'Intel Iris Plus Graphics 640',
            'AMD Radeon Pro 5500M OpenGL Engine'
        ]
    elif os_info['platform'] == 'iPhone' or os_info['platform'] == 'iPad':
        # iOS GPU info
        vendors = ['Apple Inc.', 'Apple GPU']
        renderers = [
            'Apple GPU',
            'Apple A14 GPU',
            'Apple A15 GPU'
        ]
    elif os_info['platform'] == 'Android':
        # Android GPU info
        vendors = ['Google Inc.', 'Qualcomm']
        renderers = [
            'Adreno (TM) 660',
            'Adreno (TM) 730',
            'Mali-G78 MP14',
            'PowerVR Rogue GE8320'
        ]
    else:
        # Linux GPU info
        vendors = ['Google Inc.', 'Google Inc. (Intel)', 'Google Inc. (AMD)']
        renderers = [
            'ANGLE (Intel, Mesa Intel(R) UHD Graphics 620 (CFL GT2), OpenGL 4.6)',
            'ANGLE (AMD, AMD Radeon RX 580 (POLARIS10, DRM 3.40.0, 5.10.0-9-amd64), OpenGL 4.6)',
            'ANGLE (Intel, Intel(R) HD Graphics 520 (SKL GT2), OpenGL 4.6)'
        ]
    
    # Use a seed derived from the platform for consistency
    seed = sum(ord(c) for c in os_info['platform']) % 100
    random.seed(seed)
    
    webgl_info['vendor'] = random.choice(vendors)
    webgl_info['renderer'] = random.choice(renderers)
    
    # Standard WebGL1 extensions
    webgl1_extensions = [
        "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
        "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod",
        "EXT_texture_compression_bptc", "EXT_texture_compression_rgtc",
        "EXT_texture_filter_anisotropic", "OES_element_index_uint",
        "OES_fbo_render_mipmap", "OES_standard_derivatives", "OES_texture_float",
        "OES_texture_float_linear", "OES_texture_half_float",
        "OES_texture_half_float_linear", "OES_vertex_array_object",
        "WEBGL_color_buffer_float", "WEBGL_compressed_texture_s3tc",
        "WEBGL_compressed_texture_s3tc_srgb", "WEBGL_debug_renderer_info",
        "WEBGL_debug_shaders", "WEBGL_depth_texture", "WEBGL_draw_buffers",
        "WEBGL_lose_context", "WEBGL_multi_draw"
    ]
    
    # Standard WebGL2 extensions
    webgl2_extensions = [
        "EXT_color_buffer_float", "EXT_disjoint_timer_query_webgl2",
        "EXT_float_blend", "EXT_texture_compression_bptc", 
        "EXT_texture_compression_rgtc", "EXT_texture_filter_anisotropic",
        "WEBGL_compressed_texture_s3tc", "WEBGL_compressed_texture_s3tc_srgb",
        "WEBGL_debug_renderer_info", "WEBGL_debug_shaders", "WEBGL_lose_context",
        "WEBGL_multi_draw"
    ]
    
    # Mobile devices typically support fewer extensions
    if os_info['platform'] == 'iPhone' or os_info['platform'] == 'iPad' or os_info['platform'] == 'Android':
        webgl1_extensions = [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod",
            "EXT_texture_filter_anisotropic", "OES_element_index_uint",
            "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float",
            "OES_texture_half_float_linear", "WEBGL_compressed_texture_s3tc",
            "WEBGL_debug_renderer_info", "WEBGL_depth_texture", "WEBGL_lose_context"
        ]
        
        webgl2_extensions = [
            "EXT_color_buffer_float", "EXT_float_blend", "EXT_texture_filter_anisotropic",
            "WEBGL_compressed_texture_s3tc", "WEBGL_debug_renderer_info", 
            "WEBGL_debug_shaders", "WEBGL_lose_context"
        ]
    
    # For consistency, select a subset of extensions based on the seed
    random.seed(seed)
    selected_extensions1 = sorted(random.sample(webgl1_extensions, min(20, len(webgl1_extensions))))
    selected_extensions2 = sorted(random.sample(webgl2_extensions, min(10, len(webgl2_extensions))))
    
    webgl_info['extensions_json'] = json.dumps(selected_extensions1)
    webgl_info['extensions2_json'] = json.dumps(selected_extensions2)
    
    # Shader precision
    webgl_info['precision'] = 23
    webgl_info['range_min'] = 127
    webgl_info['range_max'] = 127
    
    return webgl_info
from PyQt5.QtWidgets import (
    QWidget, QTabWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QComboBox, QCheckBox, QSpinBox, QDoubleSpinBox,
    QPushButton, QFrame, QGroupBox, QMessageBox, QProgressBar, QFormLayout,
    QSplitter, QScrollArea, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon
import random
import re

class FingerprintUI(QWidget):
    # 定义信号 - 保持简单命名以避免混淆
    detect_button_clicked = pyqtSignal()
    copy_ip_button_clicked = pyqtSignal()
    validate_timezone_clicked = pyqtSignal()
    randomize_clicked = pyqtSignal()
    reset_clicked = pyqtSignal()
    start_clicked = pyqtSignal()
    restore_clicked = pyqtSignal()
    launch_chrome_clicked = pyqtSignal()
    clean_cookies_clicked = pyqtSignal()
    save_clicked = pyqtSignal()
    generate_ua_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.tab_widget = None
        self.ip_detail_values = []
        self.user_agent_edit = None
        self.platform_combo = None
        self.browser_combo = None
        self.resolution_combo = None
        self.timezone_combo = None
        self.language_combo = None
        self.proxy_edit = None
        self.current_ip_label = None
        self.ip_progress_bar = None
        self.detect_ip_btn = None
        self.copy_ip_btn = None
        self.timezone_validation_result = None
        self.timezone_status_label = None
        self.timezone_offset_label = None
        self.auto_locale_check = None
        self.generate_ua_btn = None
        self.canvas_noise_check = None
        self.webgl_noise_check = None
        self.audio_noise_check = None
        self.webrtc_disabled_check = None
        self.font_list_edit = None
        self.mac_address_edit = None
        self.hardware_concurrency_spin = None
        self.device_memory_spin = None
        self.randomize_btn = None
        self.reset_btn = None
        self.global_start_btn = None
        self.launch_chrome_btn = None
        self.restore_btn = None
        self.clean_cookies_btn = None
        self.save_btn = None
        self.validate_timezone_btn = None
        self.system_timezone_btn = None
        self.offset_input = None
        self._init_device_data()
        
    def _init_device_data(self):
        self.user_agents = {
            "Windows Chrome": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Windows Edge": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "Windows Firefox": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "macOS Safari": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
            "macOS Chrome": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Android Chrome": "Mozilla/5.0 (Linux; Android 13; SM-S901B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
            "Android Samsung": "Mozilla/5.0 (Linux; Android 13; SM-S901B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/21.0 Chrome/110.0.5481.154 Mobile Safari/537.36",
            "Android Xiaomi": "Mozilla/5.0 (Linux; Android 13; 22081212C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36",
            "iPhone Safari": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
            "Linux Chrome": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Linux Firefox": "Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0"
        }
        self.platform_ua_map = {
            "Windows": ["Windows Chrome", "Windows Edge", "Windows Firefox"],
            "MacIntel": ["macOS Safari", "macOS Chrome"],
            "Android": ["Android Chrome", "Android Samsung", "Android Xiaomi"],
            "iPhone": ["iPhone Safari"],
            "Linux x86_64": ["Linux Chrome", "Linux Firefox"]
        }
        self.platform_fonts = {
            "Windows": "Arial,Arial Black,Arial Narrow,Calibri,Cambria,Cambria Math,Comic Sans MS,Consolas,Courier,Courier New,Georgia,Helvetica,Impact,Lucida Console,Lucida Sans Unicode,Microsoft Sans Serif,MS Gothic,MS PGothic,MS Sans Serif,MS Serif,Palatino Linotype,Segoe UI,Segoe UI Light,Segoe UI Semibold,Segoe UI Symbol,Tahoma,Times,Times New Roman,Trebuchet MS,Verdana,Wingdings",
            "MacIntel": "American Typewriter,Andale Mono,Arial,Arial Black,Arial Narrow,Arial Rounded MT Bold,Arial Unicode MS,Avenir,Avenir Next,Avenir Next Condensed,Baskerville,Big Caslon,Bodoni 72,Bodoni 72 Oldstyle,Bodoni 72 Smallcaps,Bradley Hand,Brush Script MT,Chalkboard,Chalkboard SE,Chalkduster,Charter,Cochin,Comic Sans MS,Copperplate,Courier,Courier New,Didot,DIN Alternate,DIN Condensed,Futura,Geneva,Georgia,Gill Sans,Helvetica,Helvetica Neue,Herculanum,Hoefler Text,Impact,Lucida Grande,Luminari,Marker Felt,Menlo,Microsoft Sans Serif,Monaco,Noteworthy,Optima,Palatino,Papyrus,Phosphate,Rockwell,SF Pro,Tahoma,Times,Times New Roman,Trebuchet MS,Verdana,Zapfino",
            "Android": "Roboto,Noto Sans,Noto Sans JP,Noto Sans KR,Noto Naskh Arabic,Noto Sans Thai,Noto Sans Hebrew,Noto Sans Bengali,Droid Sans,Droid Sans Fallback",
            "iPhone": "Academy Engraved LET,Al Nile,American Typewriter,Apple Color Emoji,Apple SD Gothic Neo,Arial,Arial Hebrew,Arial Rounded MT Bold,Avenir,Avenir Next,Avenir Next Condensed,Baskerville,Bodoni 72,Bradley Hand,Chalkboard SE,Cochin,Copperplate,Courier,Courier New,Damascus,Devanagari Sangam MN,Didot,DIN Alternate,DIN Condensed,Futura,Geneva,Georgia,Gill Sans,Helvetica,Helvetica Neue,Hiragino Sans,Hoefler Text,Kailasa,Menlo,Noteworthy,Optima,Palatino,Papyrus,Party LET,San Francisco,Savoye LET,SignPainter,Snell Roundhand,Symbol,Thonburi,Times New Roman,Trebuchet MS,Verdana,Zapf Dingbats",
            "Linux x86_64": "Bitstream Vera Sans,DejaVu Sans,DejaVu Sans Mono,DejaVu Serif,Droid Sans,Droid Sans Fallback,FreeMono,FreeSans,FreeSerif,Liberation Mono,Liberation Sans,Liberation Serif,Noto Color Emoji,Noto Sans,Noto Serif,Ubuntu,Ubuntu Mono"
        }
        self.platform_hardware = {
            "Windows": {"cores": 8, "memory": 8.0},
            "MacIntel": {"cores": 10, "memory": 16.0},
            "Android": {"cores": 8, "memory": 4.0},
            "iPhone": {"cores": 6, "memory": 4.0},
            "Linux x86_64": {"cores": 8, "memory": 8.0}
        }
        self.platform_resolution = {
            "Windows": ["1920x1080", "1366x768", "2560x1440", "3840x2160"],
            "MacIntel": ["1440x900", "2560x1600", "2880x1800", "3072x1920"],
            "Android": ["412x915", "360x800", "393x873", "412x892"],
            "iPhone": ["390x844", "414x896", "375x812", "414x736"],
            "Linux x86_64": ["1920x1080", "1366x768", "2560x1440"]
        }
    
    def init_ui(self):
        try:
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(15, 15, 15, 15)
            main_layout.setSpacing(15)
            
            title_label = QLabel("浏览器指纹修改器")
            title_label.setAlignment(Qt.AlignCenter)
            title_font = QFont("Microsoft YaHei", 14, QFont.Bold)
            title_label.setFont(title_font)
            main_layout.addWidget(title_label)
            
            self.tab_widget = QTabWidget()
            self.tab_widget.setDocumentMode(True)
            main_layout.addWidget(self.tab_widget)
            
            # 创建所有UI组件
            self.create_basic_tab()
            self.create_advanced_tab()
            self.create_about_tab()
            
            self.create_button_area(main_layout)
            self._apply_styles()

            # 立即连接按钮信号到内部处理函数
            self._connect_button_signals()
            
            # 初始化平台选择
            QTimer.singleShot(0, self._initialize_platform)
            
            print("UI初始化完成")
            
        except Exception as e:
            print(f"初始化UI错误: {str(e)}")
            QMessageBox.critical(self.parent, "UI初始化错误", f"创建用户界面时出错: {str(e)}")
    
    def _initialize_platform(self):
        """初始化平台和浏览器选择"""
        try:
            if self.platform_combo:
                self.platform_combo.setCurrentIndex(0)
                self.on_platform_changed(0)
                print("平台初始化完成")
        except Exception as e:
            print(f"平台初始化错误: {str(e)}")
    
    def create_basic_tab(self):
        try:
            basic_tab = QWidget()
            layout = QVBoxLayout(basic_tab)
            layout.setContentsMargins(15, 15, 15, 15)
            layout.setSpacing(15)
            
            proxy_group = QGroupBox("IP与代理设置")
            proxy_layout = QFormLayout(proxy_group)
            proxy_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
            proxy_layout.setLabelAlignment(Qt.AlignRight)
            proxy_layout.setVerticalSpacing(12)
            proxy_layout.setContentsMargins(15, 20, 15, 15)
            
            self.proxy_edit = QLineEdit()
            self.proxy_edit.setPlaceholderText("例如: http://127.0.0.1:7890 (留空表示不使用代理)")
            self.proxy_edit.setMinimumHeight(30)
            proxy_layout.addRow("代理地址:", self.proxy_edit)
            
            ip_detect_widget = QWidget()
            ip_detect_layout = QHBoxLayout(ip_detect_widget)
            ip_detect_layout.setContentsMargins(0, 0, 0, 0)
            ip_detect_layout.setSpacing(10)
            
            self.current_ip_label = QLineEdit()
            self.current_ip_label.setReadOnly(True)
            self.current_ip_label.setPlaceholderText("点击检测按钮获取IP地址")
            self.current_ip_label.setMinimumHeight(30)
            
            self.detect_ip_btn = QPushButton("检测IP")
            self.detect_ip_btn.setFixedWidth(80)
            
            self.copy_ip_btn = QPushButton("复制信息")
            self.copy_ip_btn.setFixedWidth(80)
            
            ip_detect_layout.addWidget(self.current_ip_label)
            ip_detect_layout.addWidget(self.detect_ip_btn)
            ip_detect_layout.addWidget(self.copy_ip_btn)
            
            proxy_layout.addRow("当前IP:", ip_detect_widget)
            
            self.ip_progress_bar = QProgressBar()
            self.ip_progress_bar.setRange(0, 100)
            self.ip_progress_bar.setTextVisible(False)
            self.ip_progress_bar.setFixedHeight(6)
            self.ip_progress_bar.setVisible(False)
            proxy_layout.addRow("", self.ip_progress_bar)
            
            self._create_ip_details_area(proxy_layout)
            
            self.auto_locale_check = QCheckBox("根据IP自动设置所有指纹特征")
            self.auto_locale_check.setChecked(True)
            proxy_layout.addRow("", self.auto_locale_check)
            
            layout.addWidget(proxy_group)
            
            device_group = QGroupBox("设备模拟")
            device_layout = QGridLayout(device_group)
            device_layout.setContentsMargins(15, 20, 15, 15)
            device_layout.setVerticalSpacing(15)
            device_layout.setHorizontalSpacing(20)
            
            device_layout.addWidget(QLabel("平台:"), 0, 0)
            self.platform_combo = QComboBox()
            self.platform_combo.addItems(["Windows", "MacIntel", "Android", "iPhone", "Linux x86_64"])
            device_layout.addWidget(self.platform_combo, 0, 1)
            
            device_layout.addWidget(QLabel("浏览器:"), 0, 2)
            self.browser_combo = QComboBox()
            device_layout.addWidget(self.browser_combo, 0, 3)
            
            device_layout.addWidget(QLabel("分辨率:"), 0, 4)
            self.resolution_combo = QComboBox()
            self.resolution_combo.addItems([
                "1920x1080", "1366x768", "2560x1440", "3840x2160", 
                "1440x900", "2560x1600", 
                "412x915", "390x844", "393x873", "360x800", "414x896"
            ])
            device_layout.addWidget(self.resolution_combo, 0, 5)
            
            device_layout.addWidget(QLabel("User Agent:"), 1, 0)
            self.user_agent_edit = QLineEdit()
            self.user_agent_edit.setPlaceholderText("浏览器User Agent字符串")
            self.user_agent_edit.setMinimumHeight(30)
            device_layout.addWidget(self.user_agent_edit, 1, 1, 1, 4)
            
            self.generate_ua_btn = QPushButton("随机")
            self.generate_ua_btn.setFixedWidth(60)
            device_layout.addWidget(self.generate_ua_btn, 1, 5)
            
            device_layout.addWidget(QLabel("语言:"), 2, 0)
            self.language_combo = QComboBox()
            self.language_combo.addItems([
                "zh-CN", "zh-TW", "en-US", "en-GB", "fr-FR", "de-DE", "ja-JP", "ru-RU", 
                "es-ES", "ko-KR", "it-IT", "pt-BR", "nl-NL", "id-ID", "th-TH", "vi-VN", "ar-SA"
            ])
            device_layout.addWidget(self.language_combo, 2, 1)
            
            device_layout.addWidget(QLabel("时区:"), 2, 2)
            self.timezone_combo = QComboBox()
            self.timezone_combo.addItems([
                "Asia/Shanghai", "Asia/Tokyo", "Asia/Hong_Kong", "Asia/Singapore", "Asia/Seoul",
                "America/New_York", "America/Los_Angeles", "America/Chicago", "America/Toronto",
                "Europe/London", "Europe/Paris", "Europe/Berlin", "Europe/Moscow",
                "Australia/Sydney", "Pacific/Auckland"
            ])
            device_layout.addWidget(self.timezone_combo, 2, 3, 1, 3)
            
            layout.addWidget(device_group)
            
            timezone_group = QGroupBox("时区验证")
            timezone_layout = QHBoxLayout(timezone_group)
            timezone_layout.setContentsMargins(15, 20, 15, 15)
            timezone_layout.setSpacing(20)
            
            timezone_info_widget = QWidget()
            timezone_info_layout = QVBoxLayout(timezone_info_widget)
            timezone_info_layout.setContentsMargins(0, 0, 0, 0)
            timezone_info_layout.setSpacing(10)
            
            self.timezone_status_label = QLabel("当前时区: 未检测")
            self.timezone_offset_label = QLabel("偏移量: 未知")
            timezone_info_layout.addWidget(self.timezone_status_label)
            timezone_info_layout.addWidget(self.timezone_offset_label)
            
            self.timezone_validation_result = QLineEdit()
            self.timezone_validation_result.setReadOnly(True)
            self.timezone_validation_result.setPlaceholderText("点击验证按钮检查时区设置")
            self.timezone_validation_result.setMinimumHeight(30)
            timezone_info_layout.addWidget(self.timezone_validation_result)
            
            timezone_layout.addWidget(timezone_info_widget, 3)
            
            timezone_action_widget = QWidget()
            timezone_action_layout = QVBoxLayout(timezone_action_widget)
            timezone_action_layout.setContentsMargins(0, 0, 0, 0)
            timezone_action_layout.setSpacing(10)
            
            self.validate_timezone_btn = QPushButton("验证时区设置")
            timezone_action_layout.addWidget(self.validate_timezone_btn)
            
            self.system_timezone_btn = QPushButton("系统时区修改指南")
            timezone_action_layout.addWidget(self.system_timezone_btn)
            
            offset_widget = QWidget()
            offset_layout = QHBoxLayout(offset_widget)
            offset_layout.setContentsMargins(0, 0, 0, 0)
            offset_layout.setSpacing(5)
            
            self.offset_input = QSpinBox()
            self.offset_input.setRange(-720, 720)
            self.offset_input.setValue(-480)
            self.offset_input.setSuffix(" 分钟")
            offset_layout.addWidget(QLabel("偏移:"))
            offset_layout.addWidget(self.offset_input)
            
            force_offset_btn = QPushButton("强制设置")
            force_offset_btn.setFixedWidth(80)
            offset_layout.addWidget(force_offset_btn)
            
            timezone_action_layout.addWidget(offset_widget)
            timezone_action_layout.addStretch()
            
            timezone_layout.addWidget(timezone_action_widget, 2)
            
            layout.addWidget(timezone_group)
            
            self.tab_widget.addTab(basic_tab, "基本设置")
            
        except Exception as e:
            print(f"创建基本标签页错误: {str(e)}")
            raise
    
    def _create_ip_details_area(self, parent_layout):
        ip_details_group = QGroupBox("IP详细信息")
        ip_details_layout = QGridLayout(ip_details_group)
        ip_details_layout.setVerticalSpacing(8)
        ip_details_layout.setHorizontalSpacing(15)
        
        detail_labels = ["国家/地区:", "城市/地区:", "时区:", "坐标:"]
        self.ip_detail_values = []
        
        for i, label_text in enumerate(detail_labels):
            row, col = i // 2, (i % 2) * 2
            
            label = QLabel(label_text)
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            
            value_label = QLabel("-")
            value_label.setStyleSheet("font-weight: bold;")
            self.ip_detail_values.append(value_label)
            
            ip_details_layout.addWidget(label, row, col)
            ip_details_layout.addWidget(value_label, row, col + 1)
        
        parent_layout.addRow("", ip_details_group)
    
    def create_advanced_tab(self):
        advanced_tab = QWidget()
        layout = QVBoxLayout(advanced_tab)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(15)
        
        protection_group = QGroupBox("指纹保护")
        protection_layout = QGridLayout(protection_group)
        protection_layout.setVerticalSpacing(10)
        protection_layout.setHorizontalSpacing(20)
        
        protections = [
            ("canvas_noise_check", "启用 Canvas 指纹噪声", "随机微调Canvas操作结果，防止画布指纹识别"),
            ("webgl_noise_check", "启用 WebGL 指纹噪声", "修改WebGL参数和渲染结果，防止图形卡指纹识别"),
            ("audio_noise_check", "启用音频指纹噪声", "向音频处理添加细微干扰，防止音频指纹识别"),
            ("webrtc_disabled_check", "禁用 WebRTC", "防止WebRTC泄露真实IP地址")
        ]
        
        for i, (attr_name, label_text, tooltip) in enumerate(protections):
            row, col = i // 2, i % 2
            checkbox = QCheckBox(label_text)
            checkbox.setChecked(True)
            checkbox.setToolTip(tooltip)
            setattr(self, attr_name, checkbox)
            protection_layout.addWidget(checkbox, row, col)
        
        layout.addWidget(protection_group)
        
        advanced_settings_widget = QWidget()
        advanced_settings_layout = QHBoxLayout(advanced_settings_widget)
        advanced_settings_layout.setContentsMargins(0, 0, 0, 0)
        advanced_settings_layout.setSpacing(20)
        
        hardware_group = QGroupBox("硬件设置")
        hardware_layout = QFormLayout(hardware_group)
        hardware_layout.setLabelAlignment(Qt.AlignRight)
        hardware_layout.setVerticalSpacing(12)
        
        self.hardware_concurrency_spin = QSpinBox()
        self.hardware_concurrency_spin.setRange(1, 32)
        self.hardware_concurrency_spin.setValue(8)
        hardware_layout.addRow("CPU核心数:", self.hardware_concurrency_spin)
        
        self.device_memory_spin = QDoubleSpinBox()
        self.device_memory_spin.setRange(0.5, 32)
        self.device_memory_spin.setSingleStep(0.5)
        self.device_memory_spin.setValue(8.0)
        hardware_layout.addRow("设备内存 (GB):", self.device_memory_spin)
        
        self.mac_address_edit = QLineEdit()
        hardware_layout.addRow("MAC地址:", self.mac_address_edit)
        
        generate_mac_btn = QPushButton("随机生成")
        generate_mac_btn.setFixedWidth(80)
        generate_mac_btn.clicked.connect(self.generate_random_mac)
        hardware_layout.addRow("", generate_mac_btn)
        
        advanced_settings_layout.addWidget(hardware_group)
        
        font_group = QGroupBox("字体设置")
        font_layout = QVBoxLayout(font_group)
        font_layout.setContentsMargins(15, 15, 15, 15)
        
        font_label = QLabel("系统字体列表 (逗号分隔):")
        font_layout.addWidget(font_label)
        
        self.font_list_edit = QLineEdit()
        font_layout.addWidget(self.font_list_edit)
        
        font_tip = QLabel("提示: 确保字体列表与所选平台一致，避免指纹不一致")
        font_tip.setStyleSheet("color: #666; font-style: italic;")
        font_layout.addWidget(font_tip)
        
        font_buttons_widget = QWidget()
        font_buttons_layout = QHBoxLayout(font_buttons_widget)
        font_buttons_layout.setContentsMargins(0, 0, 0, 0)
        font_buttons_layout.setSpacing(10)
        
        for platform in ["Windows", "MacIntel", "Android", "iPhone", "Linux x86_64"]:
            btn = QPushButton(platform.split(" ")[0])
            btn.setFixedHeight(25)
            btn.clicked.connect(lambda checked, p=platform: self.set_platform_fonts(p))
            font_buttons_layout.addWidget(btn)
        
        font_layout.addWidget(font_buttons_widget)
        
        advanced_settings_layout.addWidget(font_group)
        
        layout.addWidget(advanced_settings_widget)
        layout.addStretch(1)
        
        self.tab_widget.addTab(advanced_tab, "高级设置")
    
    def create_about_tab(self):
        about_tab = QWidget()
        layout = QVBoxLayout(about_tab)
        layout.setContentsMargins(20, 15, 20, 15)
        
        about_text = """<html>
        <h2 style="color: #0056b3; text-align: center;">浏览器指纹修改器</h2>
        
        <p style="margin-top: 20px;">此工具允许您修改浏览器指纹相关设置，以增强在线隐私。通过修改浏览器特征，可以减少基于浏览器指纹的追踪。</p>
        
        <h3 style="color: #28a745; margin-top: 20px;">特点:</h3>
        <ul>
          <li>支持多种设备和浏览器配置文件，确保指纹一致性</li>
          <li>自动根据IP地理位置设置匹配的浏览器指纹</li>
          <li>修改User-Agent、语言、时区等关键特征</li>
          <li>防止通过Canvas、WebGL、音频等方式的指纹识别</li>
          <li>防止WebRTC泄露真实IP地址</li>
        </ul>
        
        <h3 style="color: #28a745; margin-top: 20px;">使用方法:</h3>
        <ol>
          <li>在<strong>基本设置</strong>中选择要模拟的平台和浏览器</li>
          <li>点击<strong>检测IP</strong>获取当前IP和地区信息</li>
          <li>系统会自动设置与该地区相符的浏览器特征</li>
          <li>验证时区设置，确保与IP位置一致</li>
          <li>点击<strong>一键全局启动</strong>应用设置并开始保护</li>
        </ol>
        
        <h3 style="color: #dc3545; margin-top: 20px;">注意事项:</h3>
        <ul>
          <li>使用管理员身份运行可获得完整功能</li>
          <li>某些网站可能会检测指纹不一致的情况</li>
          <li>确保字体列表与所选平台匹配，避免被检测</li>
          <li>在关键网站使用前请先测试</li>
        </ul>
        </html>"""
        
        about_label = QLabel(about_text)
        about_label.setWordWrap(True)
        about_label.setTextFormat(Qt.RichText)
        about_label.setOpenExternalLinks(True)
        layout.addWidget(about_label)
        
        layout.addStretch(1)
        
        self.tab_widget.addTab(about_tab, "关于")

    def create_button_area(self, parent_layout):
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        self.randomize_btn = QPushButton("一键随机")
        self.randomize_btn.setFixedSize(120, 32)
        self.randomize_btn.setStyleSheet("background-color: #17a2b8; color: white;")
        button_layout.addWidget(self.randomize_btn)
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setFixedSize(120, 32)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        self.global_start_btn = QPushButton("一键全局启动")
        self.global_start_btn.setFixedSize(140, 32)
        self.global_start_btn.setStyleSheet("background-color: #28a745; color: white; font-weight: bold;")
        button_layout.addWidget(self.global_start_btn)
        
        self.launch_chrome_btn = QPushButton("启动Chrome沙箱")
        self.launch_chrome_btn.setFixedSize(140, 32)
        self.launch_chrome_btn.setStyleSheet("background-color: #fd7e14; color: white; font-weight: bold;")
        button_layout.addWidget(self.launch_chrome_btn)
        
        self.restore_btn = QPushButton("恢复环境")
        self.restore_btn.setFixedSize(120, 32)
        self.restore_btn.setStyleSheet("background-color: #dc3545; color: white;")
        self.restore_btn.setEnabled(False)
        button_layout.addWidget(self.restore_btn)
        
        self.clean_cookies_btn = QPushButton("清理Cookie和缓存")
        self.clean_cookies_btn.setFixedSize(140, 32)
        self.clean_cookies_btn.setStyleSheet("background-color: #6c757d; color: white;")
        button_layout.addWidget(self.clean_cookies_btn)
        
        button_layout.addStretch()
        
        self.save_btn = QPushButton("保存设置")
        self.save_btn.setFixedSize(120, 32)
        button_layout.addWidget(self.save_btn)
        
        parent_layout.addLayout(button_layout)
    
    # 直接将按钮与信号处理函数连接
    def _connect_button_signals(self):
        try:
            # 直接连接按钮点击事件到处理函数
            if self.detect_ip_btn:
                print("连接检测IP按钮")
                self.detect_ip_btn.clicked.connect(self._emit_detect_ip)
                
            if self.copy_ip_btn:
                self.copy_ip_btn.clicked.connect(self._emit_copy_ip)
                
            if self.validate_timezone_btn:
                self.validate_timezone_btn.clicked.connect(self._emit_validate_timezone)
                
            if self.randomize_btn:
                print("连接随机按钮")
                self.randomize_btn.clicked.connect(self._emit_randomize)
                
            if self.reset_btn:
                self.reset_btn.clicked.connect(self._emit_reset)
                
            if self.global_start_btn:
                self.global_start_btn.clicked.connect(self._emit_start)
                
            if self.restore_btn:
                self.restore_btn.clicked.connect(self._emit_restore)
                
            if self.launch_chrome_btn:
                self.launch_chrome_btn.clicked.connect(self._emit_launch_chrome)
                
            if self.clean_cookies_btn:
                self.clean_cookies_btn.clicked.connect(self._emit_clean_cookies)
                
            if self.save_btn:
                self.save_btn.clicked.connect(self._emit_save)
                
            if self.generate_ua_btn:
                self.generate_ua_btn.clicked.connect(self._emit_generate_ua)

            # 连接下拉框变更信号
            if self.platform_combo:
                self.platform_combo.currentIndexChanged.connect(self.on_platform_changed)
                
            if self.browser_combo:
                self.browser_combo.currentIndexChanged.connect(self.on_browser_changed)
                
            print("按钮信号连接完成")
        except Exception as e:
            print(f"连接按钮信号错误: {str(e)}")

    # 信号发射函数 - 这些函数用于发送信号到控制器
    def _emit_detect_ip(self):
        print("检测IP按钮被点击，发送信号")
        self.detect_button_clicked.emit()
        
    def _emit_copy_ip(self):
        print("复制IP按钮被点击，发送信号")
        self.copy_ip_button_clicked.emit()
        
    def _emit_validate_timezone(self):
        print("验证时区按钮被点击，发送信号")
        self.validate_timezone_clicked.emit()
        
    def _emit_randomize(self):
        print("随机按钮被点击，发送信号")
        self.randomize_clicked.emit()
        
    def _emit_reset(self):
        print("重置按钮被点击，发送信号")
        self.reset_clicked.emit()
        
    def _emit_start(self):
        print("启动按钮被点击，发送信号")
        self.start_clicked.emit()
        
    def _emit_restore(self):
        print("恢复按钮被点击，发送信号")
        self.restore_clicked.emit()
        
    def _emit_launch_chrome(self):
        print("启动Chrome按钮被点击，发送信号")
        self.launch_chrome_clicked.emit()
        
    def _emit_clean_cookies(self):
        print("清理Cookie按钮被点击，发送信号")
        self.clean_cookies_clicked.emit()
        
    def _emit_save(self):
        print("保存按钮被点击，发送信号")
        self.save_clicked.emit()
        
    def _emit_generate_ua(self):
        print("生成UA按钮被点击，发送信号")
        self.generate_ua_clicked.emit()
    
    def _apply_styles(self):
        style = """
        QWidget { font-family: "Microsoft YaHei", "Segoe UI", Arial; font-size: 9pt; }
        QGroupBox { font-weight: bold; border: 1px solid #bbbbbb; border-radius: 6px; margin-top: 12px; padding-top: 10px; }
        QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px; }
        QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox { border: 1px solid #c0c0c0; border-radius: 3px; padding: 4px; min-height: 22px; }
        QPushButton { background-color: #f0f0f0; border: 1px solid #c0c0c0; border-radius: 4px; padding: 4px 12px; min-height: 24px; }
        QPushButton:hover { background-color: #e0e0e0; border: 1px solid #a0a0a0; }
        QTabWidget::pane { border: 1px solid #bbbbbb; border-radius: 3px; }
        QTabBar::tab { background-color: #f0f0f0; border: 1px solid #bbbbbb; border-bottom: none; border-top-left-radius: 4px; border-top-right-radius: 4px; padding: 6px 12px; margin-right: 2px; }
        QTabBar::tab:selected { background-color: white; border-bottom: 1px solid white; }
        """
        self.setStyleSheet(style)
    
    def on_platform_changed(self, index):
        try:
            # 检查组件是否已初始化
            if not hasattr(self, 'platform_combo') or self.platform_combo is None:
                print("错误: platform_combo 未初始化")
                return
                
            if not hasattr(self, 'browser_combo') or self.browser_combo is None:
                print("错误: browser_combo 未初始化")
                return
                
            platform = self.platform_combo.currentText()
            if not platform:
                print("错误: 无法获取平台文本")
                return
                
            print(f"平台已变更为: {platform}")
            
            # 更新浏览器选项
            self.browser_combo.clear()
            
            if platform in self.platform_ua_map:
                browser_options = self.platform_ua_map[platform]
                print(f"添加浏览器选项: {browser_options}")
                
                self.browser_combo.addItems(browser_options)
                
                if self.browser_combo.count() > 0:
                    self.browser_combo.setCurrentIndex(0)
                    # 直接调用浏览器变更方法
                    self.on_browser_changed(0)
            else:
                print(f"警告: 平台 '{platform}' 在 platform_ua_map 中未找到")
            
            # 更新分辨率选项
            if self.resolution_combo and platform in self.platform_resolution:
                current_res = self.resolution_combo.currentText()
                self.resolution_combo.clear()
                self.resolution_combo.addItems(self.platform_resolution[platform])
                index = self.resolution_combo.findText(current_res)
                if index >= 0:
                    self.resolution_combo.setCurrentIndex(index)
                else:
                    self.resolution_combo.setCurrentIndex(0)
            
            # 更新硬件设置
            if platform in self.platform_hardware:
                hw = self.platform_hardware[platform]
                if self.hardware_concurrency_spin:
                    self.hardware_concurrency_spin.setValue(hw["cores"])
                if self.device_memory_spin:
                    self.device_memory_spin.setValue(hw["memory"])
                    
            # 更新字体列表
            if self.font_list_edit and platform in self.platform_fonts:
                self.font_list_edit.setText(self.platform_fonts[platform])
                
        except Exception as e:
            print(f"平台变更处理错误: {str(e)}")
    
    def on_browser_changed(self, index):
        try:
            # 检查组件是否已初始化
            if not hasattr(self, 'browser_combo') or self.browser_combo is None:
                print("错误: browser_combo 未初始化")
                return
                
            if self.browser_combo.count() == 0:
                print("错误: browser_combo 为空")
                return
                
            browser_key = self.browser_combo.currentText()
            print(f"浏览器已变更为: {browser_key}")
            
            if not browser_key:
                print("错误: 无法获取浏览器文本")
                return
                
            if not hasattr(self, 'user_agent_edit') or self.user_agent_edit is None:
                print("错误: user_agent_edit 未初始化")
                return
                
            if browser_key in self.user_agents:
                user_agent = self.user_agents[browser_key]
                self.user_agent_edit.setText(user_agent)
                print(f"已设置用户代理: {user_agent[:50]}...")
            else:
                print(f"警告: 浏览器键 '{browser_key}' 在 user_agents 中未找到")
                
        except Exception as e:
            print(f"浏览器变更处理错误: {str(e)}")
    
    def generate_random_ua(self):
        """随机生成更真实的User Agent"""
        try:
            # 检查组件是否已初始化
            if not hasattr(self, 'browser_combo') or self.browser_combo is None:
                print("错误: browser_combo 未初始化")
                return None
                
            if self.browser_combo.count() == 0:
                print("错误: browser_combo 为空")
                # 默认使用Windows Chrome
                browser_key = "Windows Chrome"
            else:
                browser_key = self.browser_combo.currentText()
                if not browser_key:
                    print("警告: 浏览器选择为空，使用默认")
                    browser_key = "Windows Chrome"
            
            print(f"正在为 {browser_key} 生成随机UA")
            
            # 如果选择的浏览器不在预定义列表中，使用默认浏览器
            if browser_key not in self.user_agents:
                print(f"警告: 浏览器键 '{browser_key}' 在 user_agents 中未找到，使用默认")
                browser_key = "Windows Chrome"
            
            if not hasattr(self, 'user_agent_edit') or self.user_agent_edit is None:
                print("错误: user_agent_edit 未初始化")
                return None
                
            base_ua = self.user_agents[browser_key]
            print(f"基础UA: {base_ua}")
            
            # 更真实的版本号生成
            chrome_versions = ["120", "119", "118", "117"]
            edge_versions = ["120", "119", "118"]
            firefox_versions = ["120", "119", "118", "117"]
            safari_versions = ["17.2", "17.1", "17.0", "16.6"]
            
            minor_version = random.randint(0, 999)
            build = random.randint(100, 999)
            modified_ua = base_ua
            
            if "Chrome/" in base_ua:
                major = random.choice(chrome_versions)
                modified_ua = re.sub(r'Chrome/\d+\.\d+\.\d+\.\d+', f'Chrome/{major}.0.{minor_version}.{build}', base_ua)
            
            if "Edg/" in base_ua:
                major = random.choice(edge_versions)
                modified_ua = re.sub(r'Edg/\d+\.\d+\.\d+\.\d+', f'Edg/{major}.0.{minor_version}.{build}', modified_ua)
            
            if "Firefox/" in base_ua:
                major = random.choice(firefox_versions)
                minor = random.randint(0, 10)
                modified_ua = re.sub(r'Firefox/\d+\.\d+', f'Firefox/{major}.{minor}', base_ua)
            
            if "Safari/" in base_ua and "Version/" in base_ua:
                version = random.choice(safari_versions)
                modified_ua = re.sub(r'Version/\d+\.\d+', f'Version/{version}', base_ua)
                # 同时更新Safari版本号
                safari_build = random.randint(600, 610)
                modified_ua = re.sub(r'Safari/\d+\.\d+', f'Safari/{safari_build}.{random.randint(1, 36)}', modified_ua)
            
            print(f"生成的UA: {modified_ua[:50]}...")
            self.user_agent_edit.setText(modified_ua)
            return modified_ua
            
        except Exception as e:
            print(f"随机生成UA错误: {str(e)}")
            return None
    
    def generate_random_mac(self):
        if self.mac_address_edit:
            mac_bytes = [random.randint(0, 255) for _ in range(6)]
            mac_bytes[0] = mac_bytes[0] & 0xFC
            mac_address = ':'.join(f'{b:02x}' for b in mac_bytes)
            self.mac_address_edit.setText(mac_address)
    
    def set_platform_fonts(self, platform):
        if self.font_list_edit and platform in self.platform_fonts:
            self.font_list_edit.setText(self.platform_fonts[platform])
    
    def set_ip_detection_in_progress(self, in_progress):
        if self.detect_ip_btn:
            self.detect_ip_btn.setEnabled(not in_progress)
        if self.ip_progress_bar:
            self.ip_progress_bar.setVisible(in_progress)
            if in_progress:
                self.ip_progress_bar.setValue(0)
    
    def update_ip_progress(self, value):
        if self.ip_progress_bar:
            self.ip_progress_bar.setValue(value)
    
    def update_ip_info(self, ip_address, ip_info):
        if self.current_ip_label:
            self.current_ip_label.setText(ip_address)
        self._update_ip_details(ip_info)
    
    def _update_ip_details(self, ip_info):
        try:
            country_text = "-"
            if 'country' in ip_info:
                country = ip_info['country']
                if 'country_code' in ip_info:
                    country_code = ip_info['country_code']
                    country_text = f"{country} ({country_code})"
                else:
                    country_text = country
            
            city_text = "-"
            city_parts = []
            if 'city' in ip_info:
                city_parts.append(ip_info['city'])
            if 'region' in ip_info:
                city_parts.append(ip_info['region'])
            if city_parts:
                city_text = ", ".join(city_parts)
            
            timezone_text = "-"
            if 'timezone' in ip_info:
                timezone = ip_info['timezone']
                if 'utc_offset' in ip_info:
                    utc_offset = ip_info['utc_offset']
                    timezone_text = f"{timezone} ({utc_offset})"
                else:
                    timezone_text = timezone
            
            coords_text = "-"
            if 'lat' in ip_info and 'lon' in ip_info:
                lat = ip_info['lat']
                lon = ip_info['lon']
                coords_text = f"{lat}, {lon}"
            
            if len(self.ip_detail_values) >= 4:
                self.ip_detail_values[0].setText(country_text)
                self.ip_detail_values[1].setText(city_text)
                self.ip_detail_values[2].setText(timezone_text)
                self.ip_detail_values[3].setText(coords_text)
                
        except Exception as e:
            print(f"更新IP详细信息错误: {str(e)}")
    
    def clear_ip_info(self):
        if self.current_ip_label:
            self.current_ip_label.setText("")
        for value_label in self.ip_detail_values:
            value_label.setText("-")
    
    def get_proxy_setting(self):
        if self.proxy_edit:
            return self.proxy_edit.text().strip()
        return ""
    
    def get_all_settings(self):
        settings = {}
        try:
            if self.user_agent_edit:
                settings['user_agent'] = self.user_agent_edit.text()
            if self.resolution_combo:
                settings['screen_resolution'] = self.resolution_combo.currentText()
            if self.timezone_combo:
                settings['timezone'] = self.timezone_combo.currentText()
            if self.language_combo:
                settings['language'] = self.language_combo.currentText()
            if self.platform_combo:
                settings['platform'] = self.platform_combo.currentText()
            if self.canvas_noise_check:
                settings['canvas_noise'] = self.canvas_noise_check.isChecked()
            if self.webgl_noise_check:
                settings['webgl_noise'] = self.webgl_noise_check.isChecked()
            if self.audio_noise_check:
                settings['audio_noise'] = self.audio_noise_check.isChecked()
            if self.webrtc_disabled_check:
                settings['webrtc_disabled'] = self.webrtc_disabled_check.isChecked()
            if self.font_list_edit:
                settings['font_list'] = self.font_list_edit.text()
            if self.mac_address_edit:
                settings['mac_address'] = self.mac_address_edit.text()
            if self.hardware_concurrency_spin:
                settings['hardware_concurrency'] = self.hardware_concurrency_spin.value()
            if self.device_memory_spin:
                settings['device_memory'] = self.device_memory_spin.value()
            proxy = self.get_proxy_setting()
            if proxy:
                settings['proxy'] = proxy
            if self.browser_combo and self.browser_combo.currentText():
                settings['browser_type'] = self.browser_combo.currentText()
            return settings
        except Exception as e:
            print(f"获取设置错误: {str(e)}")
            return {}
    
    def set_all_settings(self, settings):
        try:
            if 'user_agent' in settings and self.user_agent_edit:
                self.user_agent_edit.setText(settings['user_agent'])
                user_agent = settings['user_agent']
                matched = False
                for platform, browsers in self.platform_ua_map.items():
                    if matched:
                        break
                    for browser in browsers:
                        base_ua = self.user_agents.get(browser, "")
                        if base_ua and base_ua.split("/")[0] in user_agent:
                            if self.platform_combo.findText(platform) >= 0:
                                self.platform_combo.setCurrentText(platform)
                                self.on_platform_changed(self.platform_combo.currentIndex())
                                if self.browser_combo.findText(browser) >= 0:
                                    self.browser_combo.setCurrentText(browser)
                                matched = True
                                break
            
            if 'browser_type' in settings and self.browser_combo:
                index = self.browser_combo.findText(settings['browser_type'])
                if index >= 0:
                    self.browser_combo.setCurrentIndex(index)
            
            if 'screen_resolution' in settings and self.resolution_combo:
                index = self.resolution_combo.findText(settings['screen_resolution'])
                if index >= 0:
                    self.resolution_combo.setCurrentIndex(index)
            
            if 'timezone' in settings and self.timezone_combo:
                index = self.timezone_combo.findText(settings['timezone'])
                if index >= 0:
                    self.timezone_combo.setCurrentIndex(index)
            
            if 'language' in settings and self.language_combo:
                index = self.language_combo.findText(settings['language'])
                if index >= 0:
                    self.language_combo.setCurrentIndex(index)
            
            if 'platform' in settings and self.platform_combo:
                index = self.platform_combo.findText(settings['platform'])
                if index >= 0:
                    self.platform_combo.setCurrentIndex(index)
            
            if 'canvas_noise' in settings and self.canvas_noise_check:
                self.canvas_noise_check.setChecked(settings['canvas_noise'])
            
            if 'webgl_noise' in settings and self.webgl_noise_check:
                self.webgl_noise_check.setChecked(settings['webgl_noise'])
            
            if 'audio_noise' in settings and self.audio_noise_check:
                self.audio_noise_check.setChecked(settings['audio_noise'])
            
            if 'webrtc_disabled' in settings and self.webrtc_disabled_check:
                self.webrtc_disabled_check.setChecked(settings['webrtc_disabled'])
            
            if 'font_list' in settings and self.font_list_edit:
                self.font_list_edit.setText(settings['font_list'])
            
            if 'mac_address' in settings and self.mac_address_edit:
                self.mac_address_edit.setText(settings['mac_address'])
            
            if 'hardware_concurrency' in settings and self.hardware_concurrency_spin:
                self.hardware_concurrency_spin.setValue(settings['hardware_concurrency'])
            
            if 'device_memory' in settings and self.device_memory_spin:
                self.device_memory_spin.setValue(settings['device_memory'])
            
            if 'proxy' in settings and self.proxy_edit:
                self.proxy_edit.setText(settings['proxy'])
            
        except Exception as e:
            print(f"设置UI元素错误: {str(e)}")
    
    def update_timezone_validation(self, result_text, is_valid):
        if self.timezone_validation_result:
            self.timezone_validation_result.setText(result_text)
            if "❌" in result_text:
                self.timezone_validation_result.setStyleSheet("background-color: #ffdddd;")
            elif "⚠️" in result_text:
                self.timezone_validation_result.setStyleSheet("background-color: #ffffdd;")
            else:
                self.timezone_validation_result.setStyleSheet("background-color: #ddffdd;")
    
    def enable_restore_button(self, enabled):
        if self.restore_btn:
            self.restore_btn.setEnabled(enabled)
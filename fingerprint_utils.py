"""
Optimized Fingerprint Utilities

This module combines all utility functions from separate modules for compatibility
and convenience. It provides a unified interface for browser fingerprinting operations.
"""

import os
import json
import random
import time
import uuid
import psutil
import subprocess
import shutil
from fingerprint_core import config_dir

# Import all functions from the specialized modules
from fingerprint_utils_core import (
    clean_browser_files,
    get_os_info_from_ua,
    get_browser_info_from_ua,
    generate_consistent_webgl_info
)

from fingerprint_utils_browser import (
    find_chrome_executable,
    create_chrome_launcher_html,
    launch_chrome_with_settings
)

from fingerprint_utils_js import generate_hook_script

# Re-implement core functions to preserve backward compatibility
def launch_chrome_sandbox(settings):
    """
    Launch a Chrome sandbox environment with the current fingerprint settings.
    This is a high-level function that handles all the steps needed to launch
    Chrome with customized fingerprint settings.
    
    Args:
        settings (dict): Dictionary containing all fingerprint settings
        
    Returns:
        tuple: (success, message)
    """
    try:
        # Clean sandbox directory to ensure fresh settings
        session_id = str(uuid.uuid4())[:8]
        sandbox_dir = os.path.join(config_dir, "chrome_sandbox")
        session_dir = os.path.join(sandbox_dir, f"session_{session_id}")
        
        # Ensure directories exist
        os.makedirs(sandbox_dir, exist_ok=True)
        os.makedirs(session_dir, exist_ok=True)
        
        # Generate JavaScript hook code with the latest settings
        js_code = generate_hook_script(settings)
        
        # Save JavaScript hook to file
        js_hook_path = os.path.join(session_dir, "fingerprint_hooks.js")
        with open(js_hook_path, "w", encoding="utf-8") as f:
            f.write(js_code)
        
        # Find Chrome executable
        chrome_exe = find_chrome_executable()
        if not chrome_exe:
            return False, "Chrome browser not found. Please ensure Chrome is installed."
        
        # Save settings to a JSON file for debugging
        settings_path = os.path.join(session_dir, "applied_settings.json")
        with open(settings_path, "w", encoding="utf-8") as f:
            json.dump(settings, f, indent=2)
        
        # Calculate precise timezone offset if available
        if 'timezone' in settings and settings['timezone']:
            try:
                import pytz
                import datetime
                timezone = settings['timezone']
                target_tz = pytz.timezone(timezone)
                now = datetime.datetime.now(pytz.UTC)
                target_time = now.astimezone(target_tz)
                offset_minutes = int(target_time.utcoffset().total_seconds() / 60)
                settings['timezone_offset'] = -offset_minutes  # Store for JS generation
            except:
                # If timezone calculation fails, use a default
                settings['timezone_offset'] = 0
        
        # Launch Chrome with the settings
        return launch_chrome_with_settings(settings, js_code)
        
    except Exception as e:
        return False, f"Failed to launch Chrome sandbox: {str(e)}"
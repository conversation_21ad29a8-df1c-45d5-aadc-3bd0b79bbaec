import os
import sys
import time
import json
import shutil
import socket
import random
import ctypes
import psutil
import requests
import threading
from PyQt5.QtCore import QObject, pyqtSignal
import fingerprint_utils
from fingerprint_core import config

class FingerprintApp(QObject):
    """指纹修改应用逻辑层"""
    # 定义信号
    status_changed = pyqtSignal(str)
    ip_detection_progress = pyqtSignal(int, str)
    ip_detection_finished = pyqtSignal(bool, str, dict)
    spoofing_progress = pyqtSignal(str)
    spoofing_finished = pyqtSignal(bool, str)
    restore_progress = pyqtSignal(str)
    restore_finished = pyqtSignal(bool, str)
    
    def __init__(self):
        super().__init__()
        self.original_hosts = ""
        self.original_system_settings = {}
        self.spoofing_active = False
        
    def detect_ip_location(self, proxy=None):
        """检测当前IP和地理位置（异步执行）"""
        try:
            # 启动线程进行检测，避免阻塞UI
            detection_thread = threading.Thread(
                target=self._do_detect_ip_location,
                args=(proxy,)
            )
            detection_thread.daemon = True
            detection_thread.start()
            return True
        except Exception as e:
            self.status_changed.emit(f"IP检测错误: {str(e)}")
            return False
            
    def _do_detect_ip_location(self, proxy=None):
        """实际执行IP检测的线程函数"""
        try:
            # 更新进度
            self.ip_detection_progress.emit(10, "正在检测IP地址...")
            
            # 代理设置
            proxies = None
            if proxy:
                proxies = {
                    "http": proxy,
                    "https": proxy
                }
            
            # 尝试多个IP检测服务
            ip_services = [
                {
                    "url": "https://ipapi.co/json/",
                    "parser": self._parse_ipapi_response
                },
                {
                    "url": "https://ipinfo.io/json",
                    "parser": self._parse_ipinfo_response
                },
                {
                    "url": "https://api.ipify.org?format=json",
                    "parser": self._parse_ipify_response
                }
            ]
            
            ip_address = ""
            ip_info = {}
            
            # 轮询所有服务，直到成功
            for i, service in enumerate(ip_services):
                try:
                    progress = 20 + i * 20
                    self.ip_detection_progress.emit(progress, f"正在连接IP服务: {service['url']}...")
                    
                    response = requests.get(
                        service["url"], 
                        proxies=proxies, 
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        ip_info = service["parser"](response.json())
                        ip_address = ip_info.get("ip", "")
                        
                        if ip_address:
                            # 更新进度
                            self.ip_detection_progress.emit(90, "解析IP信息...")
                            
                            # 保存IP信息到全局变量
                            config.current_ip_info = ip_info
                            
                            self.ip_detection_progress.emit(100, "IP检测完成")
                            self.ip_detection_finished.emit(True, ip_address, ip_info)
                            return
                except Exception as e:
                    self.status_changed.emit(f"服务 {service['url']} 检测失败: {str(e)}")
                    continue
            
            # 如果所有服务都失败，尝试本地获取IP
            if not ip_address:
                self.ip_detection_progress.emit(80, "尝试获取本地IP...")
                ip_address = self._get_local_ip()
                ip_info = {"ip": ip_address}
                
            self.ip_detection_progress.emit(100, "IP检测完成")
            
            # 指示检测完成
            if ip_address:
                self.ip_detection_finished.emit(True, ip_address, ip_info)
            else:
                self.ip_detection_finished.emit(False, "未能检测到IP地址", {})
                
        except Exception as e:
            self.status_changed.emit(f"IP检测失败: {str(e)}")
            self.ip_detection_finished.emit(False, str(e), {})
    
    def _parse_ipapi_response(self, data):
        """解析ipapi.co响应"""
        return {
            "ip": data.get("ip", ""),
            "country": data.get("country_name", ""),
            "country_code": data.get("country_code", ""),
            "region": data.get("region", ""),
            "city": data.get("city", ""),
            "timezone": data.get("timezone", ""),
            "lat": data.get("latitude", 0),
            "lon": data.get("longitude", 0),
            "utc_offset": data.get("utc_offset", "")
        }
    
    def _parse_ipinfo_response(self, data):
        """解析ipinfo.io响应"""
        loc_parts = data.get("loc", "0,0").split(",")
        lat = float(loc_parts[0]) if len(loc_parts) > 0 else 0
        lon = float(loc_parts[1]) if len(loc_parts) > 1 else 0
        
        return {
            "ip": data.get("ip", ""),
            "country": data.get("country", ""),
            "country_code": data.get("country", ""),
            "region": data.get("region", ""),
            "city": data.get("city", ""),
            "timezone": data.get("timezone", ""),
            "lat": lat,
            "lon": lon,
            "utc_offset": ""
        }
    
    def _parse_ipify_response(self, data):
        """解析ipify响应，只返回IP地址"""
        ip = data.get("ip", "")
        
        # 尝试通过其他服务获取地理位置信息
        try:
            geo_response = requests.get(f"https://ipapi.co/{ip}/json/", timeout=5)
            if geo_response.status_code == 200:
                return self._parse_ipapi_response(geo_response.json())
        except:
            pass
        
        return {"ip": ip}
    
    def _get_local_ip(self):
        """获取本地IP地址（非精确）"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except:
            return "127.0.0.1"
    
    def validate_timezone(self, timezone):
        """验证时区设置并返回正确的时区偏移"""
        try:
            # 检查时区是否有效
            if timezone not in config.timezone_map:
                return False, "❌ 无效的时区名称", None
            
            # 获取当前系统时区的偏移量（分钟）
            import datetime
            import pytz
            
            # 获取目标时区
            target_tz = pytz.timezone(timezone)
            
            # 获取当前时间并转换到目标时区
            now = datetime.datetime.now(pytz.UTC)
            target_time = now.astimezone(target_tz)
            
            # 计算偏移（分钟）
            offset_minutes = int(target_time.utcoffset().total_seconds() / 60)
            offset_hours = offset_minutes / 60
            
            # 根据offset_minutes的正负生成偏移文本表示
            if offset_minutes < 0:
                offset_txt = f"UTC{offset_hours:.1f}"
            else:
                offset_txt = f"UTC+{offset_hours:.1f}"
            
            # 设置全局时区偏移量以供生成JS钩子使用
            config.current_timezone_offset = -offset_minutes  # 负数表示UTC+X
            
            # 检查系统时区与请求的时区是否匹配
            local_tz = datetime.datetime.now().astimezone().tzinfo
            local_name = str(local_tz)
            
            if local_name == timezone or (hasattr(local_tz, 'zone') and local_tz.zone == timezone):
                return True, f"✅ 时区设置正确: {timezone} ({offset_txt})", offset_minutes
            else:
                return False, f"⚠️ 系统时区与请求时区不一致: {local_name} != {timezone} ({offset_txt})", offset_minutes
                
        except Exception as e:
            self.status_changed.emit(f"时区验证错误: {str(e)}")
            return False, f"❌ 时区验证错误: {str(e)}", None
    
    def apply_fingerprint_from_ip(self, ip_info):
        """根据IP信息生成一致的指纹设置"""
        country_code = ip_info.get('country_code', '')
        
        if not country_code:
            self.status_changed.emit("无法获取国家代码，使用默认配置")
            country_code = 'DEFAULT'
        
        # 获取国家配置
        profile = config.get_country_profile(country_code)
        if not profile:
            self.status_changed.emit(f"未找到 {country_code} 的配置，使用默认配置")
            profile = config.get_country_profile('DEFAULT')
        
        # 创建推荐设置
        settings = {}
        
        # 选择合适的时区
        if 'timezone' in ip_info:
            ip_timezone = ip_info['timezone']
            # 首先尝试精确匹配
            if 'timezones' in profile and ip_timezone in profile['timezones']:
                settings['timezone'] = ip_timezone
            # 如果无法精确匹配，尝试找到同一区域的时区
            elif 'timezones' in profile and profile['timezones']:
                region = ip_timezone.split('/')[0] if '/' in ip_timezone else ''
                region_timezones = [tz for tz in profile['timezones'] if tz.startswith(f"{region}/")]
                if region_timezones:
                    settings['timezone'] = random.choice(region_timezones)
                else:
                    settings['timezone'] = random.choice(profile['timezones'])
            else:
                settings['timezone'] = ip_timezone
        elif 'timezones' in profile and profile['timezones']:
            settings['timezone'] = random.choice(profile['timezones'])
        
        # 设置与IP地区匹配的语言
        if 'languages' in profile and profile['languages']:
            settings['language'] = random.choice(profile['languages'])
        else:
            # 根据国家代码猜测语言
            language_map = {
                'CN': 'zh-CN', 'TW': 'zh-TW', 'HK': 'zh-HK', 'JP': 'ja-JP', 
                'KR': 'ko-KR', 'US': 'en-US', 'GB': 'en-GB', 'RU': 'ru-RU',
                'FR': 'fr-FR', 'DE': 'de-DE', 'ES': 'es-ES', 'IT': 'it-IT',
                'BR': 'pt-BR', 'PT': 'pt-PT'
            }
            settings['language'] = language_map.get(country_code, 'en-US')
        
        # 设置与语言/地区匹配的字体
        if 'fonts' in profile:
            settings['font_list'] = profile['fonts']
        
        # 选择合适的User Agent
        if 'user_agents' in profile and profile['user_agents']:
            settings['user_agent'] = random.choice(profile['user_agents'])
        
        # 选择合适的平台
        if 'platforms' in profile and profile['platforms']:
            settings['platform'] = random.choice(profile['platforms'])
        
        # 选择合适的屏幕分辨率
        if 'resolutions' in profile and profile['resolutions']:
            settings['screen_resolution'] = random.choice(profile['resolutions'])
        
        # 选择硬件并发数和设备内存
        if 'hardware_concurrency' in profile and profile['hardware_concurrency']:
            settings['hardware_concurrency'] = random.choice(profile['hardware_concurrency'])
        
        if 'device_memory' in profile and profile['device_memory']:
            settings['device_memory'] = random.choice(profile['device_memory'])
        
        # 保存IP信息供后续使用
        settings['ip_info'] = ip_info
        
        return settings
    
    def start_spoofing(self, settings):
        """启动指纹修改（异步执行）"""
        try:
            # 启动线程进行修改，避免阻塞UI
            spoofing_thread = threading.Thread(
                target=self._do_start_spoofing,
                args=(settings,)
            )
            spoofing_thread.daemon = True
            spoofing_thread.start()
            return True
        except Exception as e:
            self.status_changed.emit(f"启动指纹修改错误: {str(e)}")
            return False
    
    def _do_start_spoofing(self, settings):
        """实际执行指纹修改的线程函数"""
        try:
            # 保存当前系统设置以便恢复
            self._backup_system_settings()
            
            # 开始修改
            self.spoofing_progress.emit("正在应用指纹修改...")
            
            # 生成脚本
            js_script = fingerprint_utils.generate_hook_script(settings)
            
            # TODO: 实际应用更改的代码...
            # 这里应该是实际应用指纹修改的代码，根据具体情况修改
            
            # 模拟一些进度更新
            time.sleep(1)
            self.spoofing_progress.emit("修改DNS和hosts文件...")
            time.sleep(1)
            self.spoofing_progress.emit("修改浏览器启动参数...")
            time.sleep(1)
            
            # 完成
            self.spoofing_active = True
            self.spoofing_finished.emit(True, "指纹修改已成功应用")
            
        except Exception as e:
            self.spoofing_progress.emit(f"应用修改时出错: {str(e)}")
            self.spoofing_finished.emit(False, f"无法应用指纹修改: {str(e)}")
    
    def _backup_system_settings(self):
        """备份当前系统设置以便恢复"""
        self.original_system_settings = {}  # 存储原始设置
        
        # 读取主机文件
        try:
            with open(r"C:\Windows\System32\drivers\etc\hosts", "r") as f:
                self.original_hosts = f.read()
        except Exception as e:
            self.status_changed.emit(f"无法读取hosts文件: {str(e)}")
    
    def restore_environment(self):
        """恢复原始系统环境（异步执行）"""
        try:
            # 启动线程进行恢复，避免阻塞UI
            restore_thread = threading.Thread(
                target=self._do_restore_environment
            )
            restore_thread.daemon = True
            restore_thread.start()
            return True
        except Exception as e:
            self.status_changed.emit(f"启动恢复过程错误: {str(e)}")
            return False
    
    def _do_restore_environment(self):
        """实际执行环境恢复的线程函数"""
        try:
            # 开始恢复
            self.restore_progress.emit("正在恢复原始系统设置...")
            
            # 恢复主机文件
            if self.original_hosts:
                try:
                    with open(r"C:\Windows\System32\drivers\etc\hosts", "w") as f:
                        f.write(self.original_hosts)
                except Exception as e:
                    self.restore_progress.emit(f"恢复hosts文件错误: {str(e)}")
            
            # TODO: 恢复其他设置
            # 这里实现其他设置的恢复
            
            # 模拟一些进度更新
            time.sleep(1)
            self.restore_progress.emit("恢复DNS和hosts文件...")
            time.sleep(1)
            self.restore_progress.emit("恢复浏览器启动参数...")
            time.sleep(1)
            
            # 完成
            self.spoofing_active = False
            self.restore_finished.emit(True, "已成功恢复到原始系统环境")
            
        except Exception as e:
            self.restore_progress.emit(f"恢复原始环境时出错: {str(e)}")
            self.restore_finished.emit(False, f"无法恢复原始环境: {str(e)}")
    
    def launch_chrome_sandbox(self, settings):
        """启动Chrome沙箱环境，应用当前设置但不加载本机历史"""
        return fingerprint_utils.launch_chrome_sandbox(settings)
    
    def clean_cookies_cache(self):
        """清理浏览器cookie和缓存，保留账号密码相关cookie"""
        return fingerprint_utils.clean_browser_files()
    
    def save_settings(self, settings):
        """保存设置到配置文件"""
        return config.save_settings(settings)
    
    def load_settings(self):
        """从配置文件加载设置"""
        return config.load_settings()
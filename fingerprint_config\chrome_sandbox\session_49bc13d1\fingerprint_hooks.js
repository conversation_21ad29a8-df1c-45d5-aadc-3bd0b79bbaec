
    (function() {
        // Config ID: 41725 - Ensures fresh execution
        console.log("Enhanced fingerprint protection loaded - Config ID: 41725");
        
        // IP-based consistent hash for fingerprinting
        const IP_SEED = 218902;
        
        // Helper function to generate consistent values based on IP seed
        function getConsistentValue(baseValue, range = 1, precision = 4) {
            // Create a hash using the string and IP seed
            const hash = (String(baseValue).split('').reduce((a, c) => (a * 31 + c.charCodeAt(0)) >>> 0, IP_SEED) % 1000) / 1000;
            
            // Scale to the desired range and round to the specified precision
            return parseFloat((hash * range).toFixed(precision));
        }
        
        // Consistent pixel manipulation based on IP
        function getConsistentPixelOffsets(count) {
            const offsets = [];
            
            for (let i = 0; i < count; i++) {
                // Get x, y coordinates and rgb adjustment values
                offsets.push({
                    x: getConsistentValue(`x_${i}`, 1, 6),
                    y: getConsistentValue(`y_${i}`, 1, 6),
                    r: getConsistentValue(`r_${i}`, 2, 0) - 1,  // -1 to 1
                    g: getConsistentValue(`g_${i}`, 2, 0) - 1,  // -1 to 1
                    b: getConsistentValue(`b_${i}`, 2, 0) - 1   // -1 to 1
                });
            }
            
            return offsets;
        }
        
        // Common navigator properties for consistency
        const commonNavigatorProps = {
            'appCodeName': 'Mozilla',
            'appName': 'Netscape',
            'doNotTrack': null,
            'maxTouchPoints': 'False' ? 5 : 0,
            'pdfViewerEnabled': true,
            'vendorSub': '',
            'webdriver': false,
            'cookieEnabled': true,
            'vibrate': function() { return 0; }
        };
        
        // Save original objects and methods
        const originalNavigator = window.navigator;
        const originalDocument = window.document;
        const OriginalDate = window.Date;
        const originalGetTimezoneOffset = OriginalDate.prototype.getTimezoneOffset;
        const originalToString = Function.prototype.toString;

        // Canvas protection - save original methods
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
        const originalToBlob = HTMLCanvasElement.prototype.toBlob;
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        const originalPutImageData = CanvasRenderingContext2D.prototype.putImageData;
        const originalFillText = CanvasRenderingContext2D.prototype.fillText;
        const originalMeasureText = CanvasRenderingContext2D.prototype.measureText;

        // Browser environment settings
        const userAgentComponents = {
            appName: "Netscape",
            appVersion: "5.0",
            platform: "Win32",  // Use exact platform match from user agent
            vendor: "Google Inc.",
            product: "Gecko",
            oscpu: "Windows NT 10.0",
            buildID: "20231107"
        };
        
        // Navigator proxy
        const navigatorHandler = {
            get: function(target, property) {
                // Handle common properties first
                if (commonNavigatorProps.hasOwnProperty(property)) {
                    return commonNavigatorProps[property];
                }
                
                switch(property) {
                    case 'userAgent': return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
                    case 'appVersion': return userAgentComponents.appVersion;
                    case 'appName': return userAgentComponents.appName;
                    case 'platform': return "Win32";  // Always return correct platform
                    case 'vendor': return userAgentComponents.vendor;
                    case 'product': return userAgentComponents.product;
                    case 'productSub': return "20030107";
                    case 'language': return "zh-CN";
                    case 'languages': return ["zh-CN"];
                    case 'hardwareConcurrency': return 16;
                    case 'deviceMemory': return 16.0;
                    case 'userAgentData': 
                        return {
                            brands: [{"brand": "Chromium", "version": "120"}, {"brand": "Google Chrome", "version": "120"}, {"brand": "Not=A?Brand", "version": "24"}],
                            mobile: false,
                            platform: "Win32",
                            getHighEntropyValues: async (hints) => {
                                return {
                                    architecture: "x86",
                                    bitness: "64",
                                    model: "",
                                    platformVersion: "10.0",
                                    uaFullVersion: "120.0.0.0"
                                };
                            }
                        };
                    case 'plugins':
                        return Object.freeze([
                            { name: "PDF Viewer", filename: "internal-pdf-viewer", description: "Portable Document Format" },
                            { name: "Chrome PDF Viewer", filename: "internal-pdf-viewer", description: "Portable Document Format" },
                            { name: "Chromium PDF Viewer", filename: "internal-pdf-viewer", description: "Portable Document Format" },
                            { name: "Microsoft Edge PDF Viewer", filename: "internal-pdf-viewer", description: "Portable Document Format" },
                            { name: "WebKit built-in PDF", filename: "internal-pdf-viewer", description: "Portable Document Format" }
                        ]);
                    case 'mimeTypes':
                        return Object.freeze([
                            { type: "application/pdf", suffixes: "pdf", description: "Portable Document Format", __proto__: MimeType.prototype },
                            { type: "text/pdf", suffixes: "pdf", description: "Portable Document Format", __proto__: MimeType.prototype }
                        ]);
                    case 'geolocation':
                        if (originalNavigator.geolocation) {
                            return {
                                getCurrentPosition: function(success, error, options) {
                                    if (error) error({ code: 1, message: "User denied geolocation" });
                                },
                                watchPosition: function(success, error, options) {
                                    if (error) error({ code: 1, message: "User denied geolocation" });
                                    return 0;
                                },
                                clearWatch: function() {}
                            };
                        }
                        return undefined;
                    case 'connection':
                        if (originalNavigator.connection) {
                            return Object.defineProperties({}, {
                                effectiveType: { value: "4g" },
                                rtt: { value: 50 + (IP_SEED % 20) },
                                downlink: { value: 10 + (IP_SEED % 100) / 50 },
                                saveData: { value: false }
                            });
                        }
                        return undefined;
                    default:
                        return target[property];
                }
            }
        };
        
        window.navigator = new Proxy(originalNavigator, navigatorHandler);

        // Screen emulation
        Object.defineProperties(window.screen, {
            width: { value: 3840, configurable: true },
            height: { value: 2160, configurable: true },
            availWidth: { value: 3840, configurable: true },
            availHeight: { value: 2160 - 40, configurable: true },
            colorDepth: { value: 24, configurable: true },
            pixelDepth: { value: 24, configurable: true }
        });
        
        Object.defineProperties(window, {
            innerWidth: { get: function() { return 3840 - 16; } },
            innerHeight: { get: function() { return 2160 - 116; } },
            outerWidth: { get: function() { return 3840; } },
            outerHeight: { get: function() { return 2160; } },
            devicePixelRatio: { get: function() { return userAgentComponents.platform === "MacIntel" ? 2 : 1; } }
        });
        
        // Canvas protection - IP-based consistent modification
        if (true) {
            // Pre-calculate consistent pixel modifications based on IP
            const pixelOffsets = getConsistentPixelOffsets(5);
            
            // Helper function to apply consistent transformation to canvas data
            function applyConsistentTransform(imageData) {
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;
                
                // Apply the pre-calculated pixel modifications
                for (const offset of pixelOffsets) {
                    // Calculate pixel position based on relative coordinates
                    const x = Math.floor(offset.x * width);
                    const y = Math.floor(offset.y * height);
                    const pos = (y * width + x) * 4;
                    
                    // Only modify if within bounds
                    if (pos >= 0 && pos < data.length - 4) {
                        // Apply subtle RGB adjustments
                        data[pos] = Math.max(0, Math.min(255, data[pos] + offset.r));
                        data[pos + 1] = Math.max(0, Math.min(255, data[pos + 1] + offset.g));
                        data[pos + 2] = Math.max(0, Math.min(255, data[pos + 2] + offset.b));
                        // Leave alpha channel unchanged
                    }
                }
                
                return imageData;
            }
            
            // Modify getImageData to apply consistent transformation
            CanvasRenderingContext2D.prototype.getImageData = function() {
                const imageData = originalGetImageData.apply(this, arguments);
                return applyConsistentTransform(imageData);
            };
            
            // For text rendering - apply consistent subtle transformations
            CanvasRenderingContext2D.prototype.fillText = function(text, x, y, maxWidth) {
                // Only modify for potential fingerprinting text
                if (text && text.length < 20) {
                    // Apply subtle consistent position adjustment based on IP
                    const xOffset = getConsistentValue(text + 'x', 0.2, 6) - 0.1; // -0.1 to +0.1
                    const yOffset = getConsistentValue(text + 'y', 0.2, 6) - 0.1; // -0.1 to +0.1
                    
                    // Apply subtle position shift
                    return originalFillText.call(
                        this, 
                        text, 
                        x + xOffset, 
                        y + yOffset, 
                        maxWidth
                    );
                }
                
                // For normal text, use original method
                return originalFillText.apply(this, arguments);
            };
            
            // Apply consistent transformation before toDataURL
            HTMLCanvasElement.prototype.toDataURL = function() {
                // Skip transformation for very large canvases (likely not fingerprinting)
                if (this.width > 300 || this.height > 300) {
                    return originalToDataURL.apply(this, arguments);
                }
                
                const ctx = this.getContext('2d');
                if (ctx) {
                    // Get image data
                    const imageData = ctx.getImageData(0, 0, this.width, this.height);
                    
                    // Apply our consistent transformation
                    const transformedData = applyConsistentTransform(imageData);
                    
                    // Put back the transformed data
                    ctx.putImageData(transformedData, 0, 0);
                }
                
                return originalToDataURL.apply(this, arguments);
            };
            
            // Apply same protection to toBlob method
            HTMLCanvasElement.prototype.toBlob = function(callback, type, quality) {
                // Skip transformation for very large canvases (likely not fingerprinting)
                if (this.width > 300 || this.height > 300) {
                    return originalToBlob.apply(this, arguments, callback, type, quality);
                }
                
                const ctx = this.getContext('2d');
                if (ctx) {
                    // Get image data
                    const imageData = ctx.getImageData(0, 0, this.width, this.height);
                    
                    // Apply our consistent transformation
                    const transformedData = applyConsistentTransform(imageData);
                    
                    // Put back the transformed data
                    ctx.putImageData(transformedData, 0, 0);
                }
                
                return originalToBlob.apply(this, arguments);
            };
        }

        // WebGL protection
        if (true) {
            const originalGetParameterWebGL = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                switch(parameter) {
                    case 0x9245: // UNMASKED_VENDOR_WEBGL
                        return "Google Inc. (NVIDIA)";
                    case 0x9246: // UNMASKED_RENDERER_WEBGL
                        return "ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, D3D11)";
                    case 0x846D: // MAX_VERTEX_ATTRIBS
                        return 16;
                    case 0x8869: // MAX_TEXTURE_IMAGE_UNITS
                        return 16;
                    case 0x8872: // MAX_TEXTURE_SIZE
                        return 8192;
                    case 0x8B4A: // MAX_FRAGMENT_UNIFORM_VECTORS
                        return 1024;
                    case 0x8B4B: // MAX_VERTEX_UNIFORM_VECTORS
                        return 1024;
                    case 0x8B4C: // MAX_VARYING_VECTORS
                        return 30;
                    default:
                        return originalGetParameterWebGL.apply(this, arguments);
                }
            };
            
            if (window.WebGL2RenderingContext) {
                const originalGetParameterWebGL2 = WebGL2RenderingContext.prototype.getParameter;
                WebGL2RenderingContext.prototype.getParameter = function(parameter) {
                    switch(parameter) {
                        case 0x9245: // UNMASKED_VENDOR_WEBGL
                            return "Google Inc. (NVIDIA)";
                        case 0x9246: // UNMASKED_RENDERER_WEBGL
                            return "ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, D3D11)";
                        default:
                            return originalGetParameterWebGL2.apply(this, arguments);
                    }
                };
            }
            
            const originalGetSupportedExtensions = WebGLRenderingContext.prototype.getSupportedExtensions;
            WebGLRenderingContext.prototype.getSupportedExtensions = function() {
                return ["ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float", "EXT_texture_compression_bptc", "EXT_texture_filter_anisotropic", "OES_element_index_uint", "OES_fbo_render_mipmap", "OES_standard_derivatives", "OES_texture_float", "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear", "OES_vertex_array_object", "WEBGL_color_buffer_float", "WEBGL_compressed_texture_s3tc", "WEBGL_compressed_texture_s3tc_srgb", "WEBGL_debug_shaders", "WEBGL_depth_texture", "WEBGL_draw_buffers", "WEBGL_lose_context"];
            };
            
            if (window.WebGL2RenderingContext) {
                const originalGetSupportedExtensionsWebGL2 = WebGL2RenderingContext.prototype.getSupportedExtensions;
                WebGL2RenderingContext.prototype.getSupportedExtensions = function() {
                    return ["EXT_color_buffer_float", "EXT_disjoint_timer_query_webgl2", "EXT_float_blend", "EXT_texture_compression_rgtc", "EXT_texture_filter_anisotropic", "WEBGL_compressed_texture_s3tc", "WEBGL_compressed_texture_s3tc_srgb", "WEBGL_debug_renderer_info", "WEBGL_lose_context", "WEBGL_multi_draw"];
                };
            }
            
            const originalGetShaderPrecisionFormat = WebGLRenderingContext.prototype.getShaderPrecisionFormat;
            WebGLRenderingContext.prototype.getShaderPrecisionFormat = function(shaderType, precisionType) {
                const result = originalGetShaderPrecisionFormat.call(this, shaderType, precisionType);
                
                if (result) {
                    Object.defineProperties(result, {
                        precision: { get: () => 23 },
                        rangeMin: { get: () => 127 },
                        rangeMax: { get: () => 127 }
                    });
                }
                
                return result;
            };
        }

        // Audio protection - IP-based consistency
        if (true) {
            const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
            const OriginalOfflineAudioContext = window.OfflineAudioContext || window.webkitOfflineAudioContext;
            
            if (OriginalAudioContext) {
                window.AudioContext = window.webkitAudioContext = function(contextOptions) {
                    const audioCtx = new OriginalAudioContext(contextOptions);
                    
                    // Override createOscillator
                    const originalCreateOscillator = audioCtx.createOscillator;
                    audioCtx.createOscillator = function() {
                        const oscillator = originalCreateOscillator.apply(this, arguments);
                        
                        // Override frequency property
                        const originalFrequency = oscillator.frequency;
                        Object.defineProperty(oscillator, 'frequency', {
                            get: function() {
                                return {
                                    ...originalFrequency,
                                    value: originalFrequency.value,
                                    setValueAtTime: function(value, time) {
                                        // Add IP-based consistent adjustment
                                        const adjustmentFactor = getConsistentValue(value, 0.0002, 6) - 0.0001;
                                        const adjustedValue = value + adjustmentFactor;
                                        return originalFrequency.setValueAtTime(adjustedValue, time);
                                    }
                                };
                            }
                        });
                        
                        return oscillator;
                    };
                    
                    // Override createAnalyser
                    const originalCreateAnalyser = audioCtx.createAnalyser;
                    audioCtx.createAnalyser = function() {
                        const analyser = originalCreateAnalyser.apply(this, arguments);
                        
                        // Generate consistent audio frequency adjustments based on IP
                        const frequencyAdjustments = [];
                        for (let i = 0; i < 128; i++) {
                            frequencyAdjustments.push(getConsistentValue(`freq_${i}`, 0.2, 2) - 0.1); // -0.1 to +0.1
                        }
                        
                        // Override getFloatFrequencyData
                        const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                        analyser.getFloatFrequencyData = function(array) {
                            originalGetFloatFrequencyData.call(this, array);
                            
                            // Apply consistent adjustments
                            for (let i = 0; i < array.length && i < frequencyAdjustments.length; i++) {
                                array[i] += frequencyAdjustments[i];
                            }
                        };
                        
                        // Override getByteFrequencyData
                        const originalGetByteFrequencyData = analyser.getByteFrequencyData;
                        analyser.getByteFrequencyData = function(array) {
                            originalGetByteFrequencyData.call(this, array);
                            
                            // Apply consistent adjustments
                            for (let i = 0; i < array.length && i < frequencyAdjustments.length; i++) {
                                const adjustment = Math.round(frequencyAdjustments[i] * 10);
                                array[i] = Math.max(0, Math.min(255, array[i] + adjustment));
                            }
                        };
                        
                        return analyser;
                    };
                    
                    return audioCtx;
                };
            }
            
            if (OriginalOfflineAudioContext) {
                window.OfflineAudioContext = function(numOfChannels, length, sampleRate) {
                    let context;
                    
                    if (arguments.length === 1 && typeof arguments[0] === 'object') {
                        context = new OriginalOfflineAudioContext(arguments[0]);
                    } else {
                        context = new OriginalOfflineAudioContext(numOfChannels, length, sampleRate);
                    }
                    
                    const originalStartRendering = context.startRendering;
                    context.startRendering = function() {
                        const renderingPromise = originalStartRendering.call(this);
                        
                        return renderingPromise.then(renderedBuffer => {
                            // Apply IP-based consistent transformation to rendered buffer
                            for (let channel = 0; channel < renderedBuffer.numberOfChannels; channel++) {
                                const data = renderedBuffer.getChannelData(channel);
                                
                                // Only modify a small fraction of samples
                                for (let i = 0; i < 100; i++) {
                                    // Use IP-based hash to get consistent indices
                                    const idx = Math.floor(getConsistentValue(`audio_${channel}_${i}`, 1, 6) * data.length);
                                    const val = getConsistentValue(`audio_val_${channel}_${i}`, 0.0002, 6) - 0.0001;
                                    
                                    if (idx >= 0 && idx < data.length) {
                                        data[idx] += val;
                                    }
                                }
                            }
                            
                            return renderedBuffer;
                        });
                    };
                    
                    return context;
                };
                
                if (window.webkitOfflineAudioContext) {
                    window.webkitOfflineAudioContext = window.OfflineAudioContext;
                }
            }
        }
        
        // WebRTC protection
        if (true) {
            const OriginalRTC = window.RTCPeerConnection || 
                              window.webkitRTCPeerConnection || 
                              window.mozRTCPeerConnection;
            
            if (OriginalRTC) {
                window.RTCPeerConnection = function(...args) {
                    const pc = new OriginalRTC(...args);
                    
                    const originalCreateOffer = pc.createOffer;
                    pc.createOffer = function(options) {
                        return originalCreateOffer.apply(this, arguments)
                            .then(offer => {
                                if (offer && offer.sdp) {
                                    offer.sdp = offer.sdp.replace(/\b(?:[0-9]{1,3}\.?){4}\b/g, '0.0.0.0');
                                }
                                return offer;
                            });
                    };
                    
                    const originalCreateAnswer = pc.createAnswer;
                    pc.createAnswer = function() {
                        return originalCreateAnswer.apply(this, arguments)
                            .then(answer => {
                                if (answer && answer.sdp) {
                                    answer.sdp = answer.sdp.replace(/\b(?:[0-9]{1,3}\.?){4}\b/g, '0.0.0.0');
                                }
                                return answer;
                            });
                    };
                    
                    const originalAddIceCandidate = pc.addIceCandidate;
                    pc.addIceCandidate = function(candidate) {
                        if (candidate && candidate.candidate) {
                            if (/\b(?:[0-9]{1,3}\.?){4}\b/.test(candidate.candidate)) {
                                return Promise.resolve();
                            }
                        }
                        return originalAddIceCandidate.apply(this, arguments);
                    };
                    
                    const originalOnIceCandidate = Object.getOwnPropertyDescriptor(
                        Object.getPrototypeOf(pc), 'onicecandidate'
                    );
                    
                    if (originalOnIceCandidate && originalOnIceCandidate.set) {
                        Object.defineProperty(pc, 'onicecandidate', {
                            set: function(cb) {
                                const wrappedCallback = function(e) {
                                    if (e && e.candidate) {
                                        Object.defineProperty(e, 'candidate', {
                                            get: () => null
                                        });
                                    }
                                    if (cb) cb(e);
                                };
                                originalOnIceCandidate.set.call(this, wrappedCallback);
                            },
                            get: originalOnIceCandidate.get
                        });
                    }
                    
                    return pc;
                };
                
                if (OriginalRTC.prototype) {
                    window.RTCPeerConnection.prototype = OriginalRTC.prototype;
                }
                
                if (window.webkitRTCPeerConnection) {
                    window.webkitRTCPeerConnection = window.RTCPeerConnection;
                }
                if (window.mozRTCPeerConnection) {
                    window.mozRTCPeerConnection = window.RTCPeerConnection;
                }
            }
            
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
                navigator.mediaDevices.getUserMedia = function(constraints) {
                    if (constraints && (constraints.video || constraints.audio)) {
                        return Promise.reject(new DOMException('Permission denied', 'NotAllowedError'));
                    }
                    return originalGetUserMedia.apply(this, arguments);
                };
            }
        }

        // Timezone emulation with IP-based consistency
        (function() {
            const targetTimezone = "Asia/Shanghai";
            const targetOffset = 0; // Target timezone offset (minutes)
            
            // Common timezone offsets for standard regions
            const commonTimezones = {
                "UTC-08:00": 480,   // US Pacific
                "UTC-05:00": 300,   // US Eastern
                "UTC+00:00": 0,     // GMT
                "UTC+01:00": -60,   // Central European Time
                "UTC+08:00": -480,  // China/Singapore
                "UTC+09:00": -540   // Japan/Korea
            };
            
            // Find the closest common timezone
            let closestOffset = targetOffset;
            let smallestDiff = Infinity;
            
            for (const tzOffset of Object.values(commonTimezones)) {
                const diff = Math.abs(tzOffset - targetOffset);
                if (diff < smallestDiff) {
                    smallestDiff = diff;
                    closestOffset = tzOffset;
                }
            }
            
            // Override Date.prototype.getTimezoneOffset to return fixed value
            Date.prototype.getTimezoneOffset = function() {
                return closestOffset;
            };
            
            // Fix toLocale* methods to use consistent timezone
            const originalToLocaleString = Date.prototype.toLocaleString;
            const originalToLocaleDateString = Date.prototype.toLocaleDateString;
            const originalToLocaleTimeString = Date.prototype.toLocaleTimeString;
            
            Date.prototype.toLocaleString = function(...args) {
                let opts = args[1] || {};
                opts.timeZone = opts.timeZone || targetTimezone;
                return originalToLocaleString.call(this, args[0] || undefined, opts);
            };
            
            Date.prototype.toLocaleDateString = function(...args) {
                let opts = args[1] || {};
                opts.timeZone = opts.timeZone || targetTimezone;
                return originalToLocaleDateString.call(this, args[0] || undefined, opts);
            };
            
            Date.prototype.toLocaleTimeString = function(...args) {
                let opts = args[1] || {};
                opts.timeZone = opts.timeZone || targetTimezone;
                return originalToLocaleTimeString.call(this, args[0] || undefined, opts);
            };
        })();
        
        // Font protection - Simplified for better compatibility
        (function() {
            // Use more common, simplified fonts for consistency
            const supportedFonts = "Arial,Helvetica,Times New Roman,Verdana".split(/\s*,\s*/);
            
            // Create smarter font detection mechanism
            const fontDetectionPrevention = function() {
                // Save original measureText method
                const originalMeasureText = CanvasRenderingContext2D.prototype.measureText;
                
                // Smart font family detection
                const getFontFamily = function(fontStr) {
                    if (!fontStr) return '';
                    
                    const fontParts = fontStr.split(',');
                    if (fontParts.length > 0) {
                        let mainFont = fontParts[0].trim();
                        // Remove quotes
                        if ((mainFont.startsWith('"') && mainFont.endsWith('"')) || 
                            (mainFont.startsWith("'") && mainFont.endsWith("'"))) {
                            mainFont = mainFont.substring(1, mainFont.length - 1);
                        }
                        return mainFont;
                    }
                    return '';
                };
                
                // Check if font is supported
                const isFontSupported = function(fontFamily) {
                    if (!fontFamily) return false;
                    
                    return supportedFonts.some(font => 
                        font.toLowerCase() === fontFamily.toLowerCase() || 
                        fontFamily.toLowerCase().includes(font.toLowerCase())
                    );
                };
                
                // Replace measureText method
                CanvasRenderingContext2D.prototype.measureText = function(text) {
                    // Extract current font family
                    const fontFamily = getFontFamily(this.font || "");
                    
                    // Only modify for short text (fingerprinting)
                    if (text && text.length <= 3 && fontFamily && !isFontSupported(fontFamily)) {
                        // Save original font setting
                        const originalFont = this.font;
                        
                        // Temporarily modify to supported font for measurement
                        const fallbackFont = supportedFonts[0] || 'sans-serif';
                        this.font = originalFont.replace(fontFamily, fallbackFont);
                        
                        // Measure with substitute font
                        const metrics = originalMeasureText.apply(this, arguments);
                        
                        // Restore original font
                        this.font = originalFont;
                        return metrics;
                    }
                    
                    // Normal measurement
                    return originalMeasureText.apply(this, arguments);
                };
                
                // Simplified font checks
                if (document.fonts && document.fonts.check) {
                    const originalCheck = document.fonts.check;
                    document.fonts.check = function(font, text) {
                        const fontFamily = getFontFamily(font);
                        return isFontSupported(fontFamily);
                    };
                }
            };
            
            // Execute font protection
            fontDetectionPrevention();
        })();
        
        console.log('Enhanced fingerprint protection loaded - IP-based consistency enabled');
    })();
    
"""
Browser Fingerprint Modifier - Application Launcher (Fixed Version)

This script serves as the main entry point for the Browser Fingerprint Modifier application.
"""
import sys
import os
import time
import uuid
import platform
import traceback
import psutil
from PyQt5.QtWidgets import QApplication, QMainWindow, QStatusBar, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QClipboard

# 导入必要的模块
try:
    from fingerprint_ui import FingerprintUI
    from fingerprint_app import FingerprintApp
    from fingerprint_core import config
except ImportError as e:
    print(f"错误: 无法导入必要的模块: {e}")
    input("按回车键退出...")
    sys.exit(1)

class FingerprintController(QMainWindow):
    """控制器类，连接UI和应用逻辑"""
    def __init__(self):
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("浏览器指纹修改器")
        self.setMinimumSize(900, 650)
        
        # 创建UI层
        self.ui = FingerprintUI(self)
        self.setCentralWidget(self.ui)
        
        # 创建应用逻辑层
        self.app = FingerprintApp()
        
        # 设置状态栏
        self.status_bar = QStatusBar()
        self.status_bar.setFont(QFont("Microsoft YaHei", 9))
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
        
        # 初始化UI - 这将创建所有UI组件
        self.ui.init_ui()
        
        # 检查系统兼容性
        self.check_compatibility()
        
        # 立即连接信号和槽
        self.connect_signals()
        
        # 加载默认设置
        self.load_default_settings()
    
    def connect_signals(self):
        """连接所有信号和槽"""
        try:
            print("开始连接控制器信号...")
            
            # 连接UI信号到控制器方法
            self.ui.detect_button_clicked.connect(self.on_detect_ip_clicked)
            self.ui.copy_ip_button_clicked.connect(self.on_copy_ip_clicked)
            self.ui.validate_timezone_clicked.connect(self.on_validate_timezone_clicked)
            self.ui.randomize_clicked.connect(self.on_randomize_clicked)
            self.ui.reset_clicked.connect(self.on_reset_clicked)
            self.ui.start_clicked.connect(self.on_start_clicked)
            self.ui.restore_clicked.connect(self.on_restore_clicked)
            self.ui.launch_chrome_clicked.connect(self.on_launch_chrome_clicked)
            self.ui.clean_cookies_clicked.connect(self.on_clean_cookies_clicked)
            self.ui.save_clicked.connect(self.on_save_clicked)
            self.ui.generate_ua_clicked.connect(self.on_generate_ua_clicked)
            
            # 连接应用逻辑信号
            self.app.status_changed.connect(self.update_status)
            self.app.ip_detection_progress.connect(self.on_ip_detection_progress)
            self.app.ip_detection_finished.connect(self.on_ip_detection_finished)
            self.app.spoofing_progress.connect(self.update_status)
            self.app.spoofing_finished.connect(self.on_spoofing_finished)
            self.app.restore_progress.connect(self.update_status)
            self.app.restore_finished.connect(self.on_restore_finished)
            
            print("控制器信号连接完成")
        except Exception as e:
            print(f"连接信号错误: {str(e)}")
    
    def check_compatibility(self):
        """检查系统兼容性"""
        if platform.system() != "Windows":
            QMessageBox.warning(self, "系统兼容性提示", "本程序针对Windows优化设计，在其他系统上可能功能受限。")
    
    def load_default_settings(self):
        """加载默认设置或保存的设置"""
        try:
            # 尝试从配置文件加载
            settings = self.app.load_settings()
            
            if settings:
                # 使用保存的设置
                self.ui.set_all_settings(settings)
                print("已从配置文件加载设置")
            else:
                # 使用系统默认值
                default_settings = self.get_system_defaults()
                self.ui.set_all_settings(default_settings)
                print("已加载系统默认设置")
            
            self.status_bar.showMessage("已加载设置")
            
        except Exception as e:
            self.status_bar.showMessage(f"加载设置错误: {str(e)}")
    
    def get_system_defaults(self):
        """获取系统默认设置"""
        try:
            default_settings = {
                'user_agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                'screen_resolution': "1920x1080",
                'timezone': "Asia/Shanghai",
                'language': "zh-CN",
                'platform': "Windows",
                'font_list': "Arial, Helvetica, Times New Roman, Microsoft YaHei, SimSun",
                'hardware_concurrency': psutil.cpu_count(logical=True),
                'device_memory': round(psutil.virtual_memory().total / (1024.0**3), 1)
            }
            
            # 获取真实MAC地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) for elements in range(0, 48, 8)][::-1])
            default_settings['mac_address'] = mac
            
            return default_settings
            
        except Exception as e:
            print(f"获取系统默认设置错误: {str(e)}")
            return {}
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.showMessage(message)
    
    # UI事件处理器
    def on_detect_ip_clicked(self):
        """响应检测IP按钮点击"""
        try:
            print("控制器处理检测IP按钮点击")
            # 更新UI状态
            self.ui.set_ip_detection_in_progress(True)
            
            # 获取代理设置
            proxy = self.ui.get_proxy_setting()
            
            # 调用应用逻辑
            success = self.app.detect_ip_location(proxy)
            
            if not success:
                # 如果启动失败，恢复UI状态
                self.ui.set_ip_detection_in_progress(False)
                QMessageBox.critical(self, "检测错误", "无法启动IP检测")
                
        except Exception as e:
            self.ui.set_ip_detection_in_progress(False)
            self.status_bar.showMessage(f"检测IP错误: {str(e)}")
            QMessageBox.critical(self, "检测错误", f"检测IP地址时出错: {str(e)}")
    
    def on_ip_detection_progress(self, value, message):
        """处理IP检测进度"""
        self.ui.update_ip_progress(value)
    
    def on_ip_detection_finished(self, success, ip_address, ip_info):
        """处理IP检测完成"""
        # 恢复UI状态
        self.ui.set_ip_detection_in_progress(False)
        
        if success:
            # 更新UI显示IP信息
            self.ui.update_ip_info(ip_address, ip_info)
            
            # 是否自动设置指纹
            if hasattr(self.ui, 'auto_locale_check') and self.ui.auto_locale_check.isChecked():
                # 获取推荐设置
                recommended_settings = self.app.apply_fingerprint_from_ip(ip_info)
                
                # 应用设置到UI
                if recommended_settings:
                    self.ui.set_all_settings(recommended_settings)
                    QMessageBox.information(self, "自动设置", "已根据IP地理位置自动设置浏览器指纹特征")
        else:
            # 清空IP信息
            self.ui.clear_ip_info()
            QMessageBox.warning(self, "检测失败", f"无法检测IP地址: {ip_address}")
    
    def on_copy_ip_clicked(self):
        """复制IP信息到剪贴板"""
        try:
            if not config.current_ip_info:
                QMessageBox.warning(self, "无IP信息", "请先检测IP地址")
                return
            
            # 构建IP信息文本
            ip_text = []
            ip_text.append(f"IP地址: {self.ui.current_ip_label.text()}")
            
            # 添加国家/地区信息
            if 'country' in config.current_ip_info:
                country = config.current_ip_info['country']
                if 'country_code' in config.current_ip_info:
                    country_code = config.current_ip_info['country_code']
                    ip_text.append(f"国家/地区: {country} ({country_code})")
                else:
                    ip_text.append(f"国家/地区: {country}")
            
            # 添加其他信息
            if 'city' in config.current_ip_info:
                ip_text.append(f"城市: {config.current_ip_info['city']}")
            
            if 'timezone' in config.current_ip_info:
                ip_text.append(f"时区: {config.current_ip_info['timezone']}")
            
            if 'lat' in config.current_ip_info and 'lon' in config.current_ip_info:
                ip_text.append(f"坐标: {config.current_ip_info['lat']}, {config.current_ip_info['lon']}")
            
            # 复制到剪贴板
            clipboard_text = "\n".join(ip_text)
            QApplication.clipboard().setText(clipboard_text)
            
            self.status_bar.showMessage("IP信息已复制到剪贴板")
            
        except Exception as e:
            self.status_bar.showMessage(f"复制IP信息错误: {str(e)}")
            QMessageBox.critical(self, "复制错误", f"复制IP信息时出错: {str(e)}")
    
    def on_generate_ua_clicked(self):
        """处理随机UA按钮点击事件"""
        try:
            print("控制器处理生成UA按钮点击")
            ua = self.ui.generate_random_ua()  # 调用UI的方法生成UA
            if ua:
                self.status_bar.showMessage(f"已随机生成User Agent: {ua[:50]}...")
            else:
                self.status_bar.showMessage("生成User Agent失败，请检查浏览器选择")
                print("生成User Agent失败，使用默认浏览器尝试生成")
                
                # 强制尝试使用默认浏览器生成
                if self.ui.platform_combo:
                    self.ui.platform_combo.setCurrentText("Windows")
                    # 手动调用平台变更处理
                    self.ui.on_platform_changed(0)
                    # 再次尝试生成
                    ua = self.ui.generate_random_ua()
                    if ua:
                        self.status_bar.showMessage(f"已随机生成User Agent: {ua[:50]}...")
                    else:
                        QMessageBox.warning(self, "生成失败", "无法生成随机User Agent，请手动选择有效的浏览器后再试")
        except Exception as e:
            self.status_bar.showMessage(f"生成User Agent错误: {str(e)}")
            print(f"生成User Agent时出错: {str(e)}")
    
    def show_system_timezone_guide(self):
        """显示如何修改系统时区的指南"""
        guide = """
        <h3>修改系统时区指南</h3>
        <p>要通过"Different time zones"检测，您的系统时区必须与IP地址的时区一致。</p>
        
        <h4>Windows 10/11:</h4>
        <ol>
            <li>右键点击任务栏上的时间 → 调整日期/时间</li>
            <li>关闭"自动设置时间"和"自动设置时区"</li>
            <li>点击"更改时区"，选择与您的IP相匹配的时区</li>
            <li>重启浏览器</li>
        </ol>
        
        <h4>macOS:</h4>
        <ol>
            <li>系统偏好设置 → 日期与时间</li>
            <li>取消选中"自动设置时区"</li>
            <li>从下拉菜单中选择与您的IP匹配的时区</li>
            <li>重启浏览器</li>
        </ol>
        
        <p><b>提示:</b> 使用"验证时区设置"按钮来确认您的设置是否正确。</p>
        """
        
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("系统时区修改指南")
        msg_box.setTextFormat(Qt.RichText)
        msg_box.setText(guide)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    def on_validate_timezone_clicked(self):
        """验证时区设置"""
        try:
            timezone = self.ui.timezone_combo.currentText()
            is_valid, result_text, offset = self.app.validate_timezone(timezone)
            
            # 更新UI
            self.ui.update_timezone_validation(result_text, is_valid)
            
            # 更新时区信息标签
            if self.ui.timezone_status_label:
                self.ui.timezone_status_label.setText(f"当前时区: {timezone}")
            
            if self.ui.timezone_offset_label and offset is not None:
                self.ui.timezone_offset_label.setText(f"偏移量: {offset}分钟")
            
            # 如果时区不匹配，提示用户
            if not is_valid and "系统时区与请求时区不一致" in result_text:
                result = QMessageBox.question(
                    self,
                    "时区不匹配",
                    "系统时区与选择的时区不匹配，这可能导致浏览器指纹不一致。\n是否查看系统时区修改指南？",
                    QMessageBox.Yes | QMessageBox.No
                )
                if result == QMessageBox.Yes:
                    self.show_system_timezone_guide()
            
        except Exception as e:
            self.status_bar.showMessage(f"验证时区错误: {str(e)}")
            QMessageBox.critical(self, "时区验证错误", f"验证时区设置时出错: {str(e)}")
    
    def on_randomize_clicked(self):
        """随机生成设置"""
        try:
            print("控制器处理随机按钮点击")
            # 是否根据当前IP信息随机
            if config.current_ip_info:
                recommended_settings = self.app.apply_fingerprint_from_ip(config.current_ip_info)
                
                # 再次随机化一些设置
                import random
                if 'resolutions' in recommended_settings:
                    resolutions = ["1920x1080", "1366x768", "2560x1440", "3840x2160", "1280x720"]
                    recommended_settings['screen_resolution'] = random.choice(resolutions)
                
                if 'hardware_concurrency' in recommended_settings:
                    recommended_settings['hardware_concurrency'] = random.randint(2, 16)
                
                # 应用设置到UI
                self.ui.set_all_settings(recommended_settings)
            else:
                # 完全随机
                random_settings = self.get_random_settings()
                self.ui.set_all_settings(random_settings)
            
            self.status_bar.showMessage("已随机生成设置")
            QMessageBox.information(self, "随机设置", "已生成随机的浏览器指纹设置")
            
        except Exception as e:
            self.status_bar.showMessage(f"随机生成设置错误: {str(e)}")
            QMessageBox.critical(self, "随机设置错误", f"生成随机设置时出错: {str(e)}")
    
    def get_random_settings(self):
        """生成完全随机的设置"""
        import random
        
        # 获取随机国家配置
        country_codes = list(config.country_profiles.keys())
        country_codes.remove('DEFAULT')
        country_code = random.choice(country_codes)
        profile = config.get_country_profile(country_code)
        
        settings = {}
        
        # 随机User Agent
        if 'user_agents' in profile and profile['user_agents']:
            settings['user_agent'] = random.choice(profile['user_agents'])
        
        # 随机平台
        if 'platforms' in profile and profile['platforms']:
            settings['platform'] = random.choice(profile['platforms'])
        
        # 随机分辨率
        if 'resolutions' in profile and profile['resolutions']:
            settings['screen_resolution'] = random.choice(profile['resolutions'])
        
        # 随机时区
        if 'timezones' in profile and profile['timezones']:
            settings['timezone'] = random.choice(profile['timezones'])
        
        # 随机语言
        if 'languages' in profile and profile['languages']:
            settings['language'] = random.choice(profile['languages'])
        
        # 随机硬件并发数
        if 'hardware_concurrency' in profile and profile['hardware_concurrency']:
            settings['hardware_concurrency'] = random.choice(profile['hardware_concurrency'])
        
        # 随机设备内存
        if 'device_memory' in profile and profile['device_memory']:
            settings['device_memory'] = random.choice(profile['device_memory'])
        
        return settings
    
    def on_reset_clicked(self):
        """重置为默认设置"""
        self.load_default_settings()
        QMessageBox.information(self, "重置设置", "已重置为默认系统设置")
    
    def on_start_clicked(self):
        """启动指纹修改"""
        try:
            # 检查管理员权限
            if not config.is_admin():
                result = QMessageBox.warning(
                    self, 
                    "权限提示", 
                    "建议以管理员权限运行程序以获得完整功能。\n当前模式下部分系统级修改可能无法应用。\n是否继续？",
                    QMessageBox.Yes | QMessageBox.No
                )
                if result == QMessageBox.No:
                    return
            
            # 获取当前设置
            settings = self.ui.get_all_settings()
            
            # 启动指纹修改
            success = self.app.start_spoofing(settings)
            
            if success:
                self.ui.global_start_btn.setEnabled(False)
                self.status_bar.showMessage("正在应用指纹修改...")
            else:
                QMessageBox.critical(self, "启动错误", "无法启动指纹修改")
                
        except Exception as e:
            self.status_bar.showMessage(f"启动错误: {str(e)}")
            QMessageBox.critical(self, "启动错误", f"无法启动指纹修改: {str(e)}")
    
    def on_spoofing_finished(self, success, message):
        """指纹修改完成的回调"""
        self.ui.global_start_btn.setEnabled(True)
        
        if success:
            self.ui.enable_restore_button(True)
            QMessageBox.information(self, "启动成功", "指纹修改已成功应用并正在运行")
        else:
            QMessageBox.critical(self, "应用错误", message)
    
    def on_restore_clicked(self):
        """恢复原始系统环境"""
        try:
            # 确认是否恢复
            result = QMessageBox.question(
                self,
                "确认恢复",
                "是否确定恢复到原始系统环境？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if result == QMessageBox.No:
                return
            
            # 启动恢复
            success = self.app.restore_environment()
            
            if success:
                self.ui.enable_restore_button(False)
                self.status_bar.showMessage("正在恢复原始系统环境...")
            else:
                QMessageBox.critical(self, "恢复错误", "无法启动恢复过程")
                
        except Exception as e:
            self.status_bar.showMessage(f"恢复环境错误: {str(e)}")
            QMessageBox.critical(self, "恢复错误", f"无法恢复原始系统环境: {str(e)}")
    
    def on_restore_finished(self, success, message):
        """环境恢复完成的回调"""
        self.ui.enable_restore_button(True)
        
        if success:
            QMessageBox.information(self, "恢复成功", "已恢复到原始系统环境")
        else:
            QMessageBox.critical(self, "恢复错误", message)
    
    def on_launch_chrome_clicked(self):
        """启动Chrome沙箱环境"""
        try:
            # 获取当前设置
            settings = self.ui.get_all_settings()
            
            # 启动Chrome沙箱
            success, message = self.app.launch_chrome_sandbox(settings)
            
            if success:
                QMessageBox.information(self, "启动成功", message)
            else:
                QMessageBox.critical(self, "启动错误", message)
                
        except Exception as e:
            self.status_bar.showMessage(f"启动Chrome沙箱错误: {str(e)}")
            QMessageBox.critical(self, "启动错误", f"无法启动Chrome沙箱: {str(e)}")
    
    def on_clean_cookies_clicked(self):
        """清理浏览器Cookie和缓存"""
        try:
            # 确认是否清理
            result = QMessageBox.question(
                self,
                "确认清理",
                "是否确定清理所有浏览器Cookie和缓存？账号相关的Cookie将保留。",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if result == QMessageBox.No:
                return
            
            # 清理Cookie和缓存
            success, message = self.app.clean_cookies_cache()
            
            if success:
                QMessageBox.information(self, "清理完成", message)
            else:
                QMessageBox.warning(self, "清理失败", message)
                
        except Exception as e:
            self.status_bar.showMessage(f"清理Cookie和缓存错误: {str(e)}")
            QMessageBox.critical(self, "清理错误", f"清理Cookie和缓存时出错: {str(e)}")
    
    def on_save_clicked(self):
        """保存当前设置到配置文件"""
        try:
            # 获取当前设置
            settings = self.ui.get_all_settings()
            
            # 保存设置
            success, message = self.app.save_settings(settings)
            
            if success:
                QMessageBox.information(self, "保存成功", message)
            else:
                QMessageBox.critical(self, "保存错误", message)
                
        except Exception as e:
            self.status_bar.showMessage(f"保存设置错误: {str(e)}")
            QMessageBox.critical(self, "保存错误", f"无法保存设置: {str(e)}")


def check_requirements():
    """检查是否安装了所有必需的包"""
    required_packages = ['PyQt5', 'psutil', 'pytz', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少必需的包: {', '.join(missing_packages)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_environment():
    """设置应用环境"""
    # 确保配置目录存在
    config_dir = os.path.join(os.getcwd(), "fingerprint_config")
    os.makedirs(config_dir, exist_ok=True)
    
    # 创建必要的子目录
    os.makedirs(os.path.join(config_dir, "chrome_sandbox"), exist_ok=True)
    os.makedirs(os.path.join(config_dir, "profiles"), exist_ok=True)
    
    # 将当前目录设置为应用目录
    app_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(app_dir)
    
    # 将应用目录添加到Python路径
    if app_dir not in sys.path:
        sys.path.insert(0, app_dir)

# 应用程序入口
def main():
    """应用程序入口点"""
    print("╔════════════════════════════════════════════════╗")
    print("║     Browser Fingerprint Modifier - Enhanced    ║")
    print("╚════════════════════════════════════════════════╝")
    print(f"System: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version.split()[0]}")
    print("Starting application...")
    
    try:
        # 检查依赖项
        if not check_requirements():
            input("按回车键退出...")
            sys.exit(1)
        
        # 设置环境
        setup_environment()
        
        # 设置高DPI支持
        if hasattr(Qt, 'AA_EnableHighDpiScaling'):
            QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
            QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 启动应用程序
        app = QApplication(sys.argv)
        
        # 设置应用样式
        app.setStyle("Fusion")
        
        # 创建主控制器
        controller = FingerprintController()
        controller.show()
        
        # 执行应用
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n应用程序被用户终止")
        sys.exit(0)
    except Exception as e:
        print("\n" + "!" * 60)
        print("启动过程中发生严重错误:")
        print(str(e))
        print("\n堆栈跟踪:")
        traceback.print_exc()
        print("!" * 60)
        
        # 保持控制台窗口打开以查看错误
        input("\n按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
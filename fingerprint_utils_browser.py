"""
Optimized Browser Launcher Module

This module provides functions to find the Chrome executable, create launcher HTML,
and launch Chrome with custom fingerprint settings.
"""
import os
import subprocess
import shutil
import time
import json
import uuid
from fingerprint_core import config_dir

def find_chrome_executable():
    """
    Find the Chrome executable location on the system.
    
    Returns:
        str or None: Path to Chrome executable or None if not found
    """
    desktop_path = os.getcwd()
    current_dir_chrome = os.path.join(desktop_path, "chrome.exe")
    chrome_dir_exe = os.path.join(desktop_path, "chrome", "chrome.exe")
    
    # Check current directory first
    if os.path.exists(current_dir_chrome):
        return current_dir_chrome
    elif os.path.exists(chrome_dir_exe):
        return chrome_dir_exe
    
    # Check standard installation paths
    chrome_paths = [
        os.path.join(os.environ.get("ProgramFiles", ""), "Google", "Chrome", "Application", "chrome.exe"),
        os.path.join(os.environ.get("ProgramFiles(x86)", ""), "Google", "Chrome", "Application", "chrome.exe"),
        os.path.join(os.environ.get("LocalAppData", ""), "Google", "Chrome", "Application", "chrome.exe"),
        # Add Microsoft Edge paths as fallback
        os.path.join(os.environ.get("ProgramFiles", ""), "Microsoft", "Edge", "Application", "msedge.exe"),
        os.path.join(os.environ.get("ProgramFiles(x86)", ""), "Microsoft", "Edge", "Application", "msedge.exe"),
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            return path
    
    # Last attempt - try to find in PATH
    try:
        import winreg
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe") as key:
            chrome_path = winreg.QueryValue(key, None)
            if os.path.exists(chrome_path):
                return chrome_path
    except:
        pass
    
    return None

def create_chrome_launcher_html(temp_dir, settings, session_id=None):
    """
    Create an HTML file that will be opened by Chrome when launched.
    This page shows current settings and provides basic navigation.
    
    Args:
        temp_dir (str): Directory to save the launcher HTML
        settings (dict): Dictionary containing all fingerprint settings
        session_id (str, optional): Unique session ID to prevent caching
        
    Returns:
        str: Path to the created HTML file
    """
    if not session_id:
        session_id = str(uuid.uuid4())[:8]
        
    launcher_html = os.path.join(temp_dir, f"launcher_{session_id}.html")
    
    # Parse basic settings with fallbacks
    user_agent = settings.get('user_agent', '')
    screen_resolution = settings.get('screen_resolution', '1920x1080')
    platform = settings.get('platform', 'Windows')
    language = settings.get('language', 'zh-CN')
    timezone = settings.get('timezone', 'Asia/Shanghai')
    hardware_concurrency = settings.get('hardware_concurrency', 4)
    device_memory = settings.get('device_memory', 4)
    canvas_noise = settings.get('canvas_noise', True)
    webgl_noise = settings.get('webgl_noise', True)
    audio_noise = settings.get('audio_noise', True)
    webrtc_disabled = settings.get('webrtc_disabled', True)
    
    html_content = f"""
    <!DOCTYPE html>
    <html><head>
        <meta charset="UTF-8">
        <title>Browser Fingerprint Modifier - Enhanced Sandbox</title>
        <script src="fingerprint_hooks.js"></script>
        <style>
            body {{ font-family: "Microsoft YaHei", Arial, sans-serif; margin: 40px; line-height: 1.6; 
                   background-color: #f8f9fa; color: #333; }}
            h1, h2, h3 {{ color: #0056b3; }}
            .header {{ margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #dee2e6; }}
            .status {{ background-color: #e9f7ef; padding: 20px; border-radius: 5px; margin-bottom: 30px;
                     box-shadow: 0 2px 5px rgba(0,0,0,0.05); }}
            .url-input {{ width: 70%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; }}
            button {{ background-color: #0056b3; color: white; border: none; padding: 8px 15px; border-radius: 4px;
                    cursor: pointer; font-size: 14px; margin-right: 10px; }}
            button:hover {{ background-color: #003d82; }}
            table {{ width: 100%; border-collapse: collapse; margin-top: 15px; }}
            table, th, td {{ border: 1px solid #dee2e6; }}
            th, td {{ padding: 10px; text-align: left; }}
            th {{ background-color: #f8f9fa; }}
            .session-id {{ font-size: 12px; color: #6c757d; text-align: right; margin-top: 5px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Browser Fingerprint Modifier - Enhanced Sandbox (Session: {session_id})</h1>
            <p>This page is running in a sandboxed environment with enhanced fingerprint protection. Your browsing history and cookies won't be saved.</p>
            <p class="session-id">Session ID: {session_id}</p>
        </div>
        
        <div class="status">
            <h2>Current Status: Fingerprint Modification Active</h2>
            <table>
                <tr><th style="width: 30%;">Setting</th><th>Current Value</th></tr>
                <tr><td>User Agent</td><td id="uaValue">{user_agent}</td></tr>
                <tr><td>Screen Resolution</td><td>{screen_resolution}</td></tr>
                <tr><td>Platform</td><td>{platform}</td></tr>
                <tr><td>Language</td><td>{language}</td></tr>
                <tr><td>Timezone</td><td>{timezone}</td></tr>
                <tr><td>Hardware Concurrency</td><td>{hardware_concurrency}</td></tr>
                <tr><td>Device Memory</td><td>{device_memory}GB</td></tr>
                <tr><td>Fingerprint Protection</td><td>
                    Canvas({str(canvas_noise)}), 
                    WebGL({str(webgl_noise)}), 
                    Audio({str(audio_noise)}),
                    WebRTC({str(webrtc_disabled)})
                </td></tr>
            </table>
        </div>
        
        <div class="info">
            <form id="urlForm" action="#">
                <input type="text" id="urlInput" class="url-input" placeholder="https://example.com">
                <button type="submit">Visit URL</button>
                <button type="button" id="fpTestBtn">Fingerprint Test Sites</button>
            </form>
        </div>
        
        <div class="test-section" style="margin-top: 40px; background-color: #fff; padding: 20px; 
                                        border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
            <h2>Fingerprint Protection Test</h2>
            <p>Click the buttons below to test your current fingerprint protection:</p>
            
            <button id="testBtn">Run Fingerprint Test</button>
            <button id="testCanvasBtn">Test Canvas Protection</button>
            <button id="testWebGLBtn">Test WebGL Protection</button>
            <button id="testAudioBtn">Test Audio Protection</button>
            <button id="testWebRTCBtn">Test WebRTC Protection</button>
            
            <div id="testResults" style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; 
                                       border-radius: 4px; display: none;"></div>
        </div>
        
        <script>
            // Set user agent display from actual navigator
            document.getElementById('uaValue').textContent = navigator.userAgent;
            
            // URL form submission
            document.getElementById('urlForm').addEventListener('submit', function(e) {{
                e.preventDefault();
                var url = document.getElementById('urlInput').value;
                if (url && !url.startsWith('http')) {{ url = 'https://' + url; }}
                if (url) {{ window.location.href = url; }}
            }});
            
            // Fingerprint test sites button
            document.getElementById('fpTestBtn').addEventListener('click', function() {{
                const testSites = [
                    {{ name: 'AmIUnique', url: 'https://amiunique.org/' }},
                    {{ name: 'Browserleaks', url: 'https://browserleaks.com/' }},
                    {{ name: 'CreepJS', url: 'https://abrahamjuliot.github.io/creepjs/' }},
                    {{ name: 'FingerPrintJS', url: 'https://fingerprintjs.github.io/fingerprintjs/' }},
                    {{ name: 'EFF Cover Your Tracks', url: 'https://coveryourtracks.eff.org/' }}
                ];
                
                const sitesHtml = testSites.map(site => 
                    `<button onclick="window.location.href='${{site.url}}'">${{site.name}}</button>`
                ).join('');
                
                const results = document.getElementById('testResults');
                results.innerHTML = '<h3>Fingerprint Test Sites</h3>' + sitesHtml;
                results.style.display = 'block';
            }});
            
            // Main test button
            document.getElementById('testBtn').addEventListener('click', function() {{
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>Test Results</h3>';
                
                // Browser basic info test
                addResult(results, 'User Agent', navigator.userAgent);
                addResult(results, 'Platform', navigator.platform);
                addResult(results, 'Language', navigator.language);
                addResult(results, 'Hardware Concurrency', navigator.hardwareConcurrency);
                addResult(results, 'Device Memory', (navigator.deviceMemory || 'Not available') + ' GB');
                
                // Test timezone
                var date = new Date();
                addResult(results, 'Timezone Offset', date.getTimezoneOffset() + ' minutes');
                
                // Test screen
                addResult(results, 'Screen Resolution', screen.width + 'x' + screen.height);
                
                // Test Canvas fingerprint
                testCanvasFingerprint(results);
                
                // Test WebGL
                testWebGLFingerprint(results);
                
                // Test Audio fingerprint
                testAudioFingerprint(results);
                
                // Test WebRTC
                testWebRTCLeak(results);
            }});
            
            // Individual test buttons
            document.getElementById('testCanvasBtn').addEventListener('click', function() {{
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>Canvas Fingerprint Test</h3>';
                testCanvasFingerprint(results);
            }});
            
            document.getElementById('testWebGLBtn').addEventListener('click', function() {{
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>WebGL Fingerprint Test</h3>';
                testWebGLFingerprint(results);
            }});
            
            document.getElementById('testAudioBtn').addEventListener('click', function() {{
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>Audio Fingerprint Test</h3>';
                testAudioFingerprint(results);
            }});
            
            document.getElementById('testWebRTCBtn').addEventListener('click', function() {{
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>WebRTC Protection Test</h3>';
                testWebRTCLeak(results);
            }});
            
            // Helper functions
            function addResult(container, label, value) {{
                var item = document.createElement('div');
                item.style.marginBottom = '10px';
                item.innerHTML = '<strong>' + label + ':</strong> ' + value;
                container.appendChild(item);
            }}
            
            function testCanvasFingerprint(container) {{
                try {{
                    var canvas = document.createElement('canvas');
                    canvas.width = 200;
                    canvas.height = 100;
                    var ctx = canvas.getContext('2d');
                    
                    ctx.textBaseline = "alphabetic";
                    ctx.fillStyle = "#f60";
                    ctx.fillRect(125, 1, 62, 20);
                    ctx.fillStyle = "#069";
                    ctx.font = "11pt Arial";
                    ctx.fillText("Fingerprint", 2, 15);
                    ctx.fillStyle = "rgba(102, 204, 0, 0.7)";
                    ctx.font = "18pt Arial";
                    ctx.fillText("Test", 4, 45);
                    
                    var fingerprint1 = canvas.toDataURL();
                    
                    setTimeout(function() {{
                        var fingerprint2 = canvas.toDataURL();
                        var fp1Hash = fingerprint1.length;
                        var fp2Hash = fingerprint2.length;
                        
                        var testImageElem = document.createElement('div');
                        testImageElem.innerHTML = '<div style="border:1px solid #ccc; padding:10px; margin:10px 0; display:inline-block"><strong>Test Image:</strong><br><img src="' + fingerprint1 + '" style="border:1px solid #eee; margin-top:5px"></div>';
                        container.appendChild(testImageElem);
                        
                        addResult(container, 'Canvas fingerprint 1', fp1Hash);
                        addResult(container, 'Canvas fingerprint 2', fp2Hash);
                        addResult(container, 'Canvas protection', fp1Hash !== fp2Hash ? 
                                '<span style="color:green">✓ Protection detected</span>' : 
                                '<span style="color:red">✗ No protection detected</span>');
                    }}, 100);
                }} catch (e) {{
                    addResult(container, 'Canvas fingerprint test', 'Test error: ' + e.message);
                }}
            }}
            
            function testWebGLFingerprint(container) {{
                try {{
                    var canvas = document.createElement('canvas');
                    var gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    
                    if (!gl) {{
                        addResult(container, 'WebGL support', 'WebGL not supported');
                        return;
                    }}
                    
                    var debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                    
                    if (debugInfo) {{
                        var vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                        var renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                        
                        addResult(container, 'WebGL vendor', vendor);
                        addResult(container, 'WebGL renderer', renderer);
                        
                        // Test if values are consistent
                        setTimeout(function() {{
                            var vendor2 = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                            var renderer2 = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                            
                            addResult(container, 'WebGL vendor (check 2)', vendor2);
                            addResult(container, 'WebGL renderer (check 2)', renderer2);
                            
                            var isConsistent = vendor === vendor2 && renderer === renderer2;
                            addResult(container, 'WebGL values consistent', isConsistent ? 
                                    '<span style="color:green">✓ Values are consistent</span>' : 
                                    '<span style="color:red">✗ Values are inconsistent</span>');
                        }}, 500);
                    }} else {{
                        addResult(container, 'WebGL debug info', '<span style="color:green">✓ Protected - debug info blocked</span>');
                    }}
                    
                    // List WebGL extensions
                    var extensions = gl.getSupportedExtensions();
                    addResult(container, 'WebGL extension count', extensions ? extensions.length : 0);
                }} catch (e) {{
                    addResult(container, 'WebGL test', 'Test error: ' + e.message);
                }}
            }}
            
            function testAudioFingerprint(container) {{
                try {{
                    if (!window.AudioContext && !window.webkitAudioContext) {{
                        addResult(container, 'Audio Context', 'Not supported in this browser');
                        return;
                    }}
                    
                    var audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    var oscillator = audioContext.createOscillator();
                    var analyser = audioContext.createAnalyser();
                    var gain = audioContext.createGain();
                    var scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
                    
                    gain.gain.value = 0; // Mute output
                    oscillator.type = 'triangle';
                    oscillator.connect(analyser);
                    analyser.connect(scriptProcessor);
                    scriptProcessor.connect(gain);
                    gain.connect(audioContext.destination);
                    
                    scriptProcessor.onaudioprocess = function(e) {{
                        var data = new Uint8Array(analyser.frequencyBinCount);
                        analyser.getByteFrequencyData(data);
                        
                        // Get audio fingerprint by summing frequency data
                        var sum = data.reduce((a, b) => a + b, 0);
                        
                        // Run a second time to check for randomization
                        setTimeout(function() {{
                            analyser.getByteFrequencyData(data);
                            var sum2 = data.reduce((a, b) => a + b, 0);
                            
                            addResult(container, 'Audio fingerprint 1', sum);
                            addResult(container, 'Audio fingerprint 2', sum2);
                            addResult(container, 'Audio protection', sum !== sum2 ? 
                                    '<span style="color:green">✓ Protection detected</span>' : 
                                    '<span style="color:red">✗ No protection detected</span>');
                            
                            oscillator.stop();
                            audioContext.close();
                        }}, 100);
                    }};
                    
                    oscillator.start(0);
                    
                    addResult(container, 'Audio Context test', 'Running test...');
                }} catch (e) {{
                    addResult(container, 'Audio fingerprint test', 'Test error: ' + e.message);
                }}
            }}
            
            function testWebRTCLeak(container) {{
                try {{
                    if (!window.RTCPeerConnection) {{
                        addResult(container, 'WebRTC support', 'WebRTC not supported in this browser');
                        return;
                    }}
                    
                    let ips = [];
                    
                    // Create dummy connection
                    const pc = new RTCPeerConnection({{
                        iceServers: [{{
                            urls: "stun:stun.l.google.com:19302"
                        }}]
                    }});
                    
                    // On ICE candidate event, check if there's an IP
                    pc.onicecandidate = event => {{
                        if (event && event.candidate) {{
                            let line = event.candidate.candidate;
                            if (line && line.includes('srflx')) {{
                                let parts = line.split(' ');
                                let ip = parts[4];
                                if (ip && !ips.includes(ip)) ips.push(ip);
                            }}
                        }} else {{
                            // All candidates received, show results
                            if (ips.length === 0) {{
                                addResult(container, 'WebRTC protection', 
                                    '<span style="color:green">✓ No IPs leaked - protection active</span>');
                            }} else {{
                                addResult(container, 'WebRTC IP leak', 
                                    '<span style="color:red">✗ IPs leaked: ' + ips.join(', ') + '</span>');
                            }}
                            
                            // Clean up
                            pc.close();
                        }}
                    }};
                    
                    // Create offer
                    pc.createDataChannel('');
                    pc.createOffer().then(offer => pc.setLocalDescription(offer));
                    
                    // Set timeout in case onicecandidate never completes
                    setTimeout(() => {{
                        if (ips.length === 0) {{
                            addResult(container, 'WebRTC protection', 
                                '<span style="color:green">✓ No IPs leaked - protection active</span>');
                        }}
                        pc.close();
                    }}, 2000);
                    
                    addResult(container, 'WebRTC test', 'Testing for IP leaks...');
                }} catch (e) {{
                    addResult(container, 'WebRTC test', 'Test error: ' + e.message);
                }}
            }}
        </script>
    </body>
    </html>
    """
    
    with open(launcher_html, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    return launcher_html

def launch_chrome_with_settings(settings, js_script=None):
    """
    Launch Chrome with custom fingerprint settings.
    
    Args:
        settings (dict): Dictionary containing all fingerprint settings
        js_script (str, optional): JavaScript code to inject for fingerprint modification
        
    Returns:
        tuple: (success, message)
    """
    try:
        # Find Chrome executable
        chrome_exe = find_chrome_executable()
        if not chrome_exe:
            return False, "Chrome browser not found. Please ensure Chrome is installed."
        
        # Create or clean temporary directory
        temp_user_data_dir = os.path.join(config_dir, "chrome_sandbox")
        
        # Generate a unique session ID
        session_id = str(uuid.uuid4())[:8]
        
        # Create a session-specific subdirectory to avoid conflicts
        session_dir = os.path.join(temp_user_data_dir, f"session_{session_id}")
        
        # Clean previous sessions if they exist
        if os.path.exists(temp_user_data_dir):
            # Keep at most 3 recent session directories
            try:
                dirs = [d for d in os.listdir(temp_user_data_dir) if os.path.isdir(os.path.join(temp_user_data_dir, d))]
                dirs.sort(key=lambda x: os.path.getmtime(os.path.join(temp_user_data_dir, x)))
                
                # Delete all but the 2 most recent sessions
                for old_dir in dirs[:-2]:
                    shutil.rmtree(os.path.join(temp_user_data_dir, old_dir), ignore_errors=True)
            except:
                # If clean up fails, proceed anyway
                pass
        else:
            os.makedirs(temp_user_data_dir, exist_ok=True)
        
        # Create new session directory
        os.makedirs(session_dir, exist_ok=True)
        
        # Generate JS hook script if not provided
        if not js_script:
            from fingerprint_utils_js import generate_hook_script
            js_script = generate_hook_script(settings)
        
        # Save JavaScript hook file
        js_hook_path = os.path.join(session_dir, "fingerprint_hooks.js")
        with open(js_hook_path, "w", encoding="utf-8") as f:
            f.write(js_script)
        
        # Create launcher HTML
        launcher_html = create_chrome_launcher_html(session_dir, settings, session_id)
        
        # Prepare Chrome command line arguments
        cmd_parts = [
            f'"{chrome_exe}"',
            f'--user-data-dir="{session_dir}"',
            "--no-default-browser-check",
            "--no-first-run", 
            "--disable-sync",
            "--disable-web-security",
            "--allow-file-access-from-files",
            "--disable-features=WebRtcHideLocalIpsWithMdns",
            "--disable-geolocation",
            "--disable-background-timer-throttling",
            "--disable-renderer-backgrounding",
            "--disable-site-isolation-trials",
            "--disable-features=IsolateOrigins,site-per-process"
        ]
        
        # Add user agent to command line parameters for immediate effect
        if settings.get('user_agent'):
            cmd_parts.append(f'--user-agent="{settings["user_agent"]}"')
        
        # Add language if specified
        if settings.get('language'):
            cmd_parts.append(f"--lang={settings['language']}")
        
        # Disable WebRTC if requested
        if settings.get('webrtc_disabled', True):
            cmd_parts.append("--disable-webrtc")
        
        # Set timezone
        if 'timezone' in settings:
            cmd_parts.append(f'--timezone="{settings["timezone"]}"')
        
        # Add launcher HTML path
        cmd_parts.append(f'"{launcher_html.replace("\\", "/")}"')
        
        # Build command string
        cmd_str = " ".join(cmd_parts)
        
        # Log command for debugging
        with open(os.path.join(session_dir, "launch_command.log"), "w") as f:
            f.write(cmd_str)
        
        # Launch Chrome
        try:
            # First try to use shell=True which works better on Windows
            subprocess.Popen(cmd_str, shell=True)
        except:
            # Fallback to direct execution
            subprocess.Popen(cmd_parts, shell=False)
        
        # Return success message with session ID for reference
        return True, f"Chrome sandbox started with session ID: {session_id}. Fingerprint settings applied."
        
    except Exception as e:
        return False, f"Failed to launch Chrome sandbox: {str(e)}"
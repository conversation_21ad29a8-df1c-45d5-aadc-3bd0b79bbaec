
    <!DOCTYPE html>
    <html><head>
        <meta charset="UTF-8">
        <title>Browser Fingerprint Modifier - Enhanced Sandbox</title>
        <script src="fingerprint_hooks.js"></script>
        <style>
            body { font-family: "Microsoft YaHei", Arial, sans-serif; margin: 40px; line-height: 1.6; 
                   background-color: #f8f9fa; color: #333; }
            h1, h2, h3 { color: #0056b3; }
            .header { margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #dee2e6; }
            .status { background-color: #e9f7ef; padding: 20px; border-radius: 5px; margin-bottom: 30px;
                     box-shadow: 0 2px 5px rgba(0,0,0,0.05); }
            .url-input { width: 70%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; }
            button { background-color: #0056b3; color: white; border: none; padding: 8px 15px; border-radius: 4px;
                    cursor: pointer; font-size: 14px; margin-right: 10px; }
            button:hover { background-color: #003d82; }
            table { width: 100%; border-collapse: collapse; margin-top: 15px; }
            table, th, td { border: 1px solid #dee2e6; }
            th, td { padding: 10px; text-align: left; }
            th { background-color: #f8f9fa; }
            .session-id { font-size: 12px; color: #6c757d; text-align: right; margin-top: 5px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Browser Fingerprint Modifier - Enhanced Sandbox (Session: b210dfd7)</h1>
            <p>This page is running in a sandboxed environment with enhanced fingerprint protection. Your browsing history and cookies won't be saved.</p>
            <p class="session-id">Session ID: b210dfd7</p>
        </div>
        
        <div class="status">
            <h2>Current Status: Fingerprint Modification Active</h2>
            <table>
                <tr><th style="width: 30%;">Setting</th><th>Current Value</th></tr>
                <tr><td>User Agent</td><td id="uaValue">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36</td></tr>
                <tr><td>Screen Resolution</td><td>2560x1440</td></tr>
                <tr><td>Platform</td><td>Windows</td></tr>
                <tr><td>Language</td><td>zh-CN</td></tr>
                <tr><td>Timezone</td><td>Asia/Shanghai</td></tr>
                <tr><td>Hardware Concurrency</td><td>8</td></tr>
                <tr><td>Device Memory</td><td>8.0GB</td></tr>
                <tr><td>Fingerprint Protection</td><td>
                    Canvas(True), 
                    WebGL(True), 
                    Audio(True),
                    WebRTC(True)
                </td></tr>
            </table>
        </div>
        
        <div class="info">
            <form id="urlForm" action="#">
                <input type="text" id="urlInput" class="url-input" placeholder="https://example.com">
                <button type="submit">Visit URL</button>
                <button type="button" id="fpTestBtn">Fingerprint Test Sites</button>
            </form>
        </div>
        
        <div class="test-section" style="margin-top: 40px; background-color: #fff; padding: 20px; 
                                        border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
            <h2>Fingerprint Protection Test</h2>
            <p>Click the buttons below to test your current fingerprint protection:</p>
            
            <button id="testBtn">Run Fingerprint Test</button>
            <button id="testCanvasBtn">Test Canvas Protection</button>
            <button id="testWebGLBtn">Test WebGL Protection</button>
            <button id="testAudioBtn">Test Audio Protection</button>
            <button id="testWebRTCBtn">Test WebRTC Protection</button>
            
            <div id="testResults" style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; 
                                       border-radius: 4px; display: none;"></div>
        </div>
        
        <script>
            // Set user agent display from actual navigator
            document.getElementById('uaValue').textContent = navigator.userAgent;
            
            // URL form submission
            document.getElementById('urlForm').addEventListener('submit', function(e) {
                e.preventDefault();
                var url = document.getElementById('urlInput').value;
                if (url && !url.startsWith('http')) { url = 'https://' + url; }
                if (url) { window.location.href = url; }
            });
            
            // Fingerprint test sites button
            document.getElementById('fpTestBtn').addEventListener('click', function() {
                const testSites = [
                    { name: 'AmIUnique', url: 'https://amiunique.org/' },
                    { name: 'Browserleaks', url: 'https://browserleaks.com/' },
                    { name: 'CreepJS', url: 'https://abrahamjuliot.github.io/creepjs/' },
                    { name: 'FingerPrintJS', url: 'https://fingerprintjs.github.io/fingerprintjs/' },
                    { name: 'EFF Cover Your Tracks', url: 'https://coveryourtracks.eff.org/' }
                ];
                
                const sitesHtml = testSites.map(site => 
                    `<button onclick="window.location.href='${site.url}'">${site.name}</button>`
                ).join('');
                
                const results = document.getElementById('testResults');
                results.innerHTML = '<h3>Fingerprint Test Sites</h3>' + sitesHtml;
                results.style.display = 'block';
            });
            
            // Main test button
            document.getElementById('testBtn').addEventListener('click', function() {
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>Test Results</h3>';
                
                // Browser basic info test
                addResult(results, 'User Agent', navigator.userAgent);
                addResult(results, 'Platform', navigator.platform);
                addResult(results, 'Language', navigator.language);
                addResult(results, 'Hardware Concurrency', navigator.hardwareConcurrency);
                addResult(results, 'Device Memory', (navigator.deviceMemory || 'Not available') + ' GB');
                
                // Test timezone
                var date = new Date();
                addResult(results, 'Timezone Offset', date.getTimezoneOffset() + ' minutes');
                
                // Test screen
                addResult(results, 'Screen Resolution', screen.width + 'x' + screen.height);
                
                // Test Canvas fingerprint
                testCanvasFingerprint(results);
                
                // Test WebGL
                testWebGLFingerprint(results);
                
                // Test Audio fingerprint
                testAudioFingerprint(results);
                
                // Test WebRTC
                testWebRTCLeak(results);
            });
            
            // Individual test buttons
            document.getElementById('testCanvasBtn').addEventListener('click', function() {
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>Canvas Fingerprint Test</h3>';
                testCanvasFingerprint(results);
            });
            
            document.getElementById('testWebGLBtn').addEventListener('click', function() {
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>WebGL Fingerprint Test</h3>';
                testWebGLFingerprint(results);
            });
            
            document.getElementById('testAudioBtn').addEventListener('click', function() {
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>Audio Fingerprint Test</h3>';
                testAudioFingerprint(results);
            });
            
            document.getElementById('testWebRTCBtn').addEventListener('click', function() {
                var results = document.getElementById('testResults');
                results.style.display = 'block';
                results.innerHTML = '<h3>WebRTC Protection Test</h3>';
                testWebRTCLeak(results);
            });
            
            // Helper functions
            function addResult(container, label, value) {
                var item = document.createElement('div');
                item.style.marginBottom = '10px';
                item.innerHTML = '<strong>' + label + ':</strong> ' + value;
                container.appendChild(item);
            }
            
            function testCanvasFingerprint(container) {
                try {
                    var canvas = document.createElement('canvas');
                    canvas.width = 200;
                    canvas.height = 100;
                    var ctx = canvas.getContext('2d');
                    
                    ctx.textBaseline = "alphabetic";
                    ctx.fillStyle = "#f60";
                    ctx.fillRect(125, 1, 62, 20);
                    ctx.fillStyle = "#069";
                    ctx.font = "11pt Arial";
                    ctx.fillText("Fingerprint", 2, 15);
                    ctx.fillStyle = "rgba(102, 204, 0, 0.7)";
                    ctx.font = "18pt Arial";
                    ctx.fillText("Test", 4, 45);
                    
                    var fingerprint1 = canvas.toDataURL();
                    
                    setTimeout(function() {
                        var fingerprint2 = canvas.toDataURL();
                        var fp1Hash = fingerprint1.length;
                        var fp2Hash = fingerprint2.length;
                        
                        var testImageElem = document.createElement('div');
                        testImageElem.innerHTML = '<div style="border:1px solid #ccc; padding:10px; margin:10px 0; display:inline-block"><strong>Test Image:</strong><br><img src="' + fingerprint1 + '" style="border:1px solid #eee; margin-top:5px"></div>';
                        container.appendChild(testImageElem);
                        
                        addResult(container, 'Canvas fingerprint 1', fp1Hash);
                        addResult(container, 'Canvas fingerprint 2', fp2Hash);
                        addResult(container, 'Canvas protection', fp1Hash !== fp2Hash ? 
                                '<span style="color:green">✓ Protection detected</span>' : 
                                '<span style="color:red">✗ No protection detected</span>');
                    }, 100);
                } catch (e) {
                    addResult(container, 'Canvas fingerprint test', 'Test error: ' + e.message);
                }
            }
            
            function testWebGLFingerprint(container) {
                try {
                    var canvas = document.createElement('canvas');
                    var gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    
                    if (!gl) {
                        addResult(container, 'WebGL support', 'WebGL not supported');
                        return;
                    }
                    
                    var debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                    
                    if (debugInfo) {
                        var vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                        var renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                        
                        addResult(container, 'WebGL vendor', vendor);
                        addResult(container, 'WebGL renderer', renderer);
                        
                        // Test if values are consistent
                        setTimeout(function() {
                            var vendor2 = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                            var renderer2 = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                            
                            addResult(container, 'WebGL vendor (check 2)', vendor2);
                            addResult(container, 'WebGL renderer (check 2)', renderer2);
                            
                            var isConsistent = vendor === vendor2 && renderer === renderer2;
                            addResult(container, 'WebGL values consistent', isConsistent ? 
                                    '<span style="color:green">✓ Values are consistent</span>' : 
                                    '<span style="color:red">✗ Values are inconsistent</span>');
                        }, 500);
                    } else {
                        addResult(container, 'WebGL debug info', '<span style="color:green">✓ Protected - debug info blocked</span>');
                    }
                    
                    // List WebGL extensions
                    var extensions = gl.getSupportedExtensions();
                    addResult(container, 'WebGL extension count', extensions ? extensions.length : 0);
                } catch (e) {
                    addResult(container, 'WebGL test', 'Test error: ' + e.message);
                }
            }
            
            function testAudioFingerprint(container) {
                try {
                    if (!window.AudioContext && !window.webkitAudioContext) {
                        addResult(container, 'Audio Context', 'Not supported in this browser');
                        return;
                    }
                    
                    var audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    var oscillator = audioContext.createOscillator();
                    var analyser = audioContext.createAnalyser();
                    var gain = audioContext.createGain();
                    var scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
                    
                    gain.gain.value = 0; // Mute output
                    oscillator.type = 'triangle';
                    oscillator.connect(analyser);
                    analyser.connect(scriptProcessor);
                    scriptProcessor.connect(gain);
                    gain.connect(audioContext.destination);
                    
                    scriptProcessor.onaudioprocess = function(e) {
                        var data = new Uint8Array(analyser.frequencyBinCount);
                        analyser.getByteFrequencyData(data);
                        
                        // Get audio fingerprint by summing frequency data
                        var sum = data.reduce((a, b) => a + b, 0);
                        
                        // Run a second time to check for randomization
                        setTimeout(function() {
                            analyser.getByteFrequencyData(data);
                            var sum2 = data.reduce((a, b) => a + b, 0);
                            
                            addResult(container, 'Audio fingerprint 1', sum);
                            addResult(container, 'Audio fingerprint 2', sum2);
                            addResult(container, 'Audio protection', sum !== sum2 ? 
                                    '<span style="color:green">✓ Protection detected</span>' : 
                                    '<span style="color:red">✗ No protection detected</span>');
                            
                            oscillator.stop();
                            audioContext.close();
                        }, 100);
                    };
                    
                    oscillator.start(0);
                    
                    addResult(container, 'Audio Context test', 'Running test...');
                } catch (e) {
                    addResult(container, 'Audio fingerprint test', 'Test error: ' + e.message);
                }
            }
            
            function testWebRTCLeak(container) {
                try {
                    if (!window.RTCPeerConnection) {
                        addResult(container, 'WebRTC support', 'WebRTC not supported in this browser');
                        return;
                    }
                    
                    let ips = [];
                    
                    // Create dummy connection
                    const pc = new RTCPeerConnection({
                        iceServers: [{
                            urls: "stun:stun.l.google.com:19302"
                        }]
                    });
                    
                    // On ICE candidate event, check if there's an IP
                    pc.onicecandidate = event => {
                        if (event && event.candidate) {
                            let line = event.candidate.candidate;
                            if (line && line.includes('srflx')) {
                                let parts = line.split(' ');
                                let ip = parts[4];
                                if (ip && !ips.includes(ip)) ips.push(ip);
                            }
                        } else {
                            // All candidates received, show results
                            if (ips.length === 0) {
                                addResult(container, 'WebRTC protection', 
                                    '<span style="color:green">✓ No IPs leaked - protection active</span>');
                            } else {
                                addResult(container, 'WebRTC IP leak', 
                                    '<span style="color:red">✗ IPs leaked: ' + ips.join(', ') + '</span>');
                            }
                            
                            // Clean up
                            pc.close();
                        }
                    };
                    
                    // Create offer
                    pc.createDataChannel('');
                    pc.createOffer().then(offer => pc.setLocalDescription(offer));
                    
                    // Set timeout in case onicecandidate never completes
                    setTimeout(() => {
                        if (ips.length === 0) {
                            addResult(container, 'WebRTC protection', 
                                '<span style="color:green">✓ No IPs leaked - protection active</span>');
                        }
                        pc.close();
                    }, 2000);
                    
                    addResult(container, 'WebRTC test', 'Testing for IP leaks...');
                } catch (e) {
                    addResult(container, 'WebRTC test', 'Test error: ' + e.message);
                }
            }
        </script>
    </body>
    </html>
    
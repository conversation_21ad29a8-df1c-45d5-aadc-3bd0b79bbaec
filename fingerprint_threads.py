import os
import json
import time
import uuid
import random
import winreg
import subprocess
import requests
from PIL import Image, ImageDraw
from PyQt5.QtCore import QThread, pyqtSignal

# 导入本地模块
from fingerprint_core import config_dir, config
import fingerprint_utils as utils

class IPDetectionThread(QThread):
    """IP检测线程类"""
    progress_signal = pyqtSignal(int, str)  # 进度值, 状态消息
    finished_signal = pyqtSignal(bool, str, dict)  # 成功/失败, IP地址, IP信息
    
    def __init__(self, proxies=None):
        super().__init__()
        self.proxies = proxies or {}
    
    def run(self):
        """执行IP检测"""
        try:
            self.progress_signal.emit(10, "正在连接IP检测服务...")
            
            # 使用公共IP API检测IP和地区信息
            api_urls = [
                'https://api.ipify.org?format=json',  # 获取IP
                'https://ipapi.co/json/',             # 获取IP和地区信息
                'https://ip-api.com/json/',           # 备用API
            ]
            
            ip_info = None
            ip_address = None
            
            # 尝试获取IP地址
            for i, api_url in enumerate(api_urls):
                progress = 10 + i * 20
                self.progress_signal.emit(progress, f"尝试API {i+1}...")
                
                try:
                    response = requests.get(api_url, proxies=self.proxies, timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        
                        if 'ip' in data:
                            ip_address = data['ip']
                        
                        # 检查是否包含地区信息
                        if any(key in data for key in ['country', 'countryCode', 'timezone', 'languages']):
                            ip_info = data
                            break
                        
                        # 如果仅有IP信息但无地区信息，继续尝试下一个API
                except Exception as e:
                    print(f"API {api_url} 请求失败: {str(e)}")
                    continue
            
            # 如果只获取到IP但没有地区信息，使用其他API查询
            if ip_address and not ip_info:
                self.progress_signal.emit(70, "获取详细地理位置信息...")
                try:
                    response = requests.get(f"https://ipapi.co/{ip_address}/json/", proxies=self.proxies, timeout=5)
                    if response.status_code == 200:
                        ip_info = response.json()
                except Exception as e:
                    print(f"获取IP详细信息失败: {str(e)}")
            
            self.progress_signal.emit(90, "处理地理位置信息...")
            
            # 添加检测时间
            if ip_info:
                ip_info['detection_time'] = time.strftime("%Y-%m-%d %H:%M:%S")
            
            time.sleep(0.5)  # 给用户一些视觉反馈
            
            if ip_address:
                self.progress_signal.emit(100, f"检测完成: {ip_address}")
                self.finished_signal.emit(True, ip_address, ip_info or {})
            else:
                self.progress_signal.emit(100, "检测失败")
                self.finished_signal.emit(False, "检测失败", {})
                
        except Exception as e:
            print(f"IP检测线程错误: {str(e)}")
            self.progress_signal.emit(100, f"检测错误: {str(e)}")
            self.finished_signal.emit(False, f"错误: {str(e)}", {})

class RestoreThread(QThread):
    """恢复原始系统环境的线程"""
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self):
        super().__init__()
        self.config_dir = config_dir
        
    def run(self):
        try:
            self.progress_signal.emit("正在恢复原始系统环境...")
            
            # 1. 读取保存的原始环境设置
            backup_path = os.path.join(self.config_dir, "original_settings.json")
            if os.path.exists(backup_path):
                with open(backup_path, "r") as f:
                    original_settings = json.load(f)
                
                # 2. 恢复系统设置
                # ...恢复代码略，与原代码相同

                self.progress_signal.emit("原始系统环境已恢复")
                self.finished_signal.emit(True, "系统环境已成功恢复")
            else:
                self.finished_signal.emit(False, "未找到原始系统设置备份，无法恢复")
        except Exception as e:
            self.progress_signal.emit(f"恢复原始环境错误: {str(e)}")
            self.finished_signal.emit(False, f"恢复系统环境时出错: {str(e)}")

class SpoofingThread(QThread):
    """后台线程执行指纹修改操作"""
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, settings=None):
        super().__init__()
        self.settings = settings or {}
        self.original_settings = {}
    
    def run(self):
        try:
            self.progress_signal.emit("正在应用指纹修改...")
            
            # 0. 备份当前系统设置
            self.backup_original_settings()
            
            # 1-8. 执行各项指纹修改操作
            # ...修改代码略，与原代码相同

            self.finished_signal.emit(True, "指纹修改已成功应用")
        except Exception as e:
            self.finished_signal.emit(False, f"应用指纹修改时出错: {str(e)}")
    
    def backup_original_settings(self):
        """备份原始系统设置，以便后续还原"""
        try:
            self.progress_signal.emit("正在备份原始系统设置...")
            
            # ...备份代码略，与原代码相同
            
            self.progress_signal.emit("原始系统设置已备份")
        except Exception as e:
            self.progress_signal.emit(f"备份系统设置错误: {str(e)}")
"""
Optimized Fingerprint Controller

This module contains the FingerprintController class that connects the UI and application logic,
managing the flow of data and commands between them.
"""
from PyQt5.QtWidgets import QMainWindow, QStatusBar, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QClipboard, QIcon
import platform
import psutil
import uuid
import os
import json
import shutil
import random
import time

from fingerprint_ui import Fingerprint<PERSON>
from fingerprint_app import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fingerprint_core import config

class FingerprintController(QMainWindow):
    """
    Controller class that connects the UI and application logic.
    
    This class handles all interactions between the UI and the application,
    ensuring proper data flow and command execution.
    """
    def __init__(self):
        super().__init__()
        
        # Set window properties
        self.setWindowTitle("Browser Fingerprint Modifier")
        self.setMinimumSize(900, 650)
        
        # Create UI layer
        self.ui = FingerprintUI(self)
        self.setCentralWidget(self.ui)
        
        # Create application logic layer
        self.app = FingerprintApp()
        
        # Set up status bar
        self.status_bar = QStatusBar()
        self.status_bar.setFont(QFont("Microsoft YaHei", 9))
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
        
        # Initialize UI - this will create all UI components
        self.ui.init_ui()
        
        # Check system compatibility
        self.check_compatibility()
        
        # Connect signals and slots
        self.connect_signals()
        
        # Load default settings
        self.load_default_settings()
    
    def connect_signals(self):
        """Connect all signals and slots between UI and application logic."""
        try:
            # Connect UI signals to controller methods
            self.ui.detect_button_clicked.connect(self.on_detect_ip_clicked)
            self.ui.copy_ip_button_clicked.connect(self.on_copy_ip_clicked)
            self.ui.validate_timezone_clicked.connect(self.on_validate_timezone_clicked)
            self.ui.randomize_clicked.connect(self.on_randomize_clicked)
            self.ui.reset_clicked.connect(self.on_reset_clicked)
            self.ui.start_clicked.connect(self.on_start_clicked)
            self.ui.restore_clicked.connect(self.on_restore_clicked)
            self.ui.launch_chrome_clicked.connect(self.on_launch_chrome_clicked)
            self.ui.clean_cookies_clicked.connect(self.on_clean_cookies_clicked)
            self.ui.save_clicked.connect(self.on_save_clicked)
            self.ui.generate_ua_clicked.connect(self.on_generate_ua_clicked)
            
            # Connect application logic signals
            self.app.status_changed.connect(self.update_status)
            self.app.ip_detection_progress.connect(self.on_ip_detection_progress)
            self.app.ip_detection_finished.connect(self.on_ip_detection_finished)
            self.app.spoofing_progress.connect(self.update_status)
            self.app.spoofing_finished.connect(self.on_spoofing_finished)
            self.app.restore_progress.connect(self.update_status)
            self.app.restore_finished.connect(self.on_restore_finished)
        except Exception as e:
            print(f"Signal connection error: {str(e)}")
    
    def check_compatibility(self):
        """Check system compatibility and show warnings if necessary."""
        if platform.system() != "Windows":
            QMessageBox.warning(self, "System Compatibility Notice", 
                               "This application is optimized for Windows. Some features may be limited on other operating systems.")
    
    def load_default_settings(self):
        """Load default settings or previously saved settings."""
        try:
            # Try to load from configuration file
            settings = self.app.load_settings()
            
            if settings:
                # Use saved settings
                self.ui.set_all_settings(settings)
            else:
                # Use system defaults
                default_settings = self.get_system_defaults()
                self.ui.set_all_settings(default_settings)
            
            self.status_bar.showMessage("Settings loaded")
            
        except Exception as e:
            self.status_bar.showMessage(f"Error loading settings: {str(e)}")
    
    def get_system_defaults(self):
        """Get default settings based on the system configuration."""
        try:
            default_settings = {
                'user_agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                'screen_resolution': "1920x1080",
                'timezone': "Asia/Shanghai",
                'language': "zh-CN",
                'platform': "Windows",
                'font_list': "Arial, Helvetica, Times New Roman, Microsoft YaHei, SimSun",
                'hardware_concurrency': psutil.cpu_count(logical=True),
                'device_memory': round(psutil.virtual_memory().total / (1024.0**3), 1),
                'canvas_noise': True,
                'webgl_noise': True,
                'audio_noise': True,
                'webrtc_disabled': True
            }
            
            # Get MAC address
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) for elements in range(0, 48, 8)][::-1])
            default_settings['mac_address'] = mac
            
            return default_settings
            
        except Exception as e:
            print(f"Error getting system defaults: {str(e)}")
            return {}
    
    def update_status(self, message):
        """Update status bar with a message."""
        self.status_bar.showMessage(message)
    
    # UI event handlers
    def on_detect_ip_clicked(self):
        """Handle IP detection button click."""
        try:
            # Update UI state
            self.ui.set_ip_detection_in_progress(True)
            
            # Get proxy setting
            proxy = self.ui.get_proxy_setting()
            
            # Call application logic
            success = self.app.detect_ip_location(proxy)
            
            if not success:
                # If startup fails, restore UI state
                self.ui.set_ip_detection_in_progress(False)
                QMessageBox.critical(self, "Detection Error", "Could not start IP detection")
                
        except Exception as e:
            self.ui.set_ip_detection_in_progress(False)
            self.status_bar.showMessage(f"IP detection error: {str(e)}")
            QMessageBox.critical(self, "Detection Error", f"Error detecting IP address: {str(e)}")
    
    def on_ip_detection_progress(self, value, message):
        """Handle IP detection progress updates."""
        self.ui.update_ip_progress(value)
    
    def on_ip_detection_finished(self, success, ip_address, ip_info):
        """Handle IP detection completion."""
        # Restore UI state
        self.ui.set_ip_detection_in_progress(False)
        
        if success:
            # Update UI with IP information
            self.ui.update_ip_info(ip_address, ip_info)
            
            # Automatically set fingerprint if enabled
            if hasattr(self.ui, 'auto_locale_check') and self.ui.auto_locale_check.isChecked():
                # Get recommended settings
                recommended_settings = self.app.apply_fingerprint_from_ip(ip_info)
                
                # Apply settings to UI
                if recommended_settings:
                    self.ui.set_all_settings(recommended_settings)
                    QMessageBox.information(self, "Auto Setup", 
                                           "Browser fingerprint characteristics have been automatically set based on IP geolocation")
        else:
            # Clear IP information
            self.ui.clear_ip_info()
            QMessageBox.warning(self, "Detection Failed", f"Could not detect IP address: {ip_address}")
    
    def on_copy_ip_clicked(self):
        """Copy IP information to clipboard."""
        try:
            if not config.current_ip_info:
                QMessageBox.warning(self, "No IP Information", "Please detect IP address first")
                return
            
            # Build IP information text
            ip_text = []
            ip_text.append(f"IP Address: {self.ui.current_ip_label.text()}")
            
            # Add country/region information
            if 'country' in config.current_ip_info:
                country = config.current_ip_info['country']
                if 'country_code' in config.current_ip_info:
                    country_code = config.current_ip_info['country_code']
                    ip_text.append(f"Country/Region: {country} ({country_code})")
                else:
                    ip_text.append(f"Country/Region: {country}")
            
            # Add other information
            if 'city' in config.current_ip_info:
                ip_text.append(f"City: {config.current_ip_info['city']}")
            
            if 'timezone' in config.current_ip_info:
                ip_text.append(f"Timezone: {config.current_ip_info['timezone']}")
            
            if 'lat' in config.current_ip_info and 'lon' in config.current_ip_info:
                ip_text.append(f"Coordinates: {config.current_ip_info['lat']}, {config.current_ip_info['lon']}")
            
            # Copy to clipboard
            clipboard_text = "\n".join(ip_text)
            self.app.clipboard().setText(clipboard_text)
            
            self.status_bar.showMessage("IP information copied to clipboard")
            
        except Exception as e:
            self.status_bar.showMessage(f"Error copying IP information: {str(e)}")
            QMessageBox.critical(self, "Copy Error", f"Error copying IP information: {str(e)}")
    
    def on_generate_ua_clicked(self):
        """Handle random User-Agent generation button click."""
        try:
            ua = self.ui.generate_random_ua()
            if ua:
                self.status_bar.showMessage(f"Randomly generated User Agent: {ua[:50]}...")
            else:
                self.status_bar.showMessage("Failed to generate User Agent, check browser selection")
                
                # Force try using default browser
                if self.ui.platform_combo:
                    self.ui.platform_combo.setCurrentText("Windows")
                    # Manually call platform change handler
                    self.ui.on_platform_changed(0)
                    # Try generating again
                    ua = self.ui.generate_random_ua()
                    if ua:
                        self.status_bar.showMessage(f"Randomly generated User Agent: {ua[:50]}...")
                    else:
                        QMessageBox.warning(self, "Generation Failed", 
                                           "Could not generate random User Agent, please manually select a valid browser and try again")
        except Exception as e:
            self.status_bar.showMessage(f"Error generating User Agent: {str(e)}")
            print(f"Error generating User Agent: {str(e)}")
    
    def show_system_timezone_guide(self):
        """Show guide on how to modify system timezone."""
        guide = """
        <h3>System Timezone Modification Guide</h3>
        <p>To pass "Different time zones" detection, your system timezone must match your IP address timezone.</p>
        
        <h4>Windows 10/11:</h4>
        <ol>
            <li>Right-click on the time in the taskbar → Adjust date/time</li>
            <li>Turn off "Set time automatically" and "Set time zone automatically"</li>
            <li>Click "Change time zone" and select the timezone that matches your IP</li>
            <li>Restart your browser</li>
        </ol>
        
        <h4>macOS:</h4>
        <ol>
            <li>System Preferences → Date & Time</li>
            <li>Uncheck "Set time zone automatically"</li>
            <li>Select your IP's timezone from the dropdown menu</li>
            <li>Restart your browser</li>
        </ol>
        
        <p><b>Tip:</b> Use the "Validate Timezone Settings" button to confirm your settings are correct.</p>
        """
        
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("System Timezone Modification Guide")
        msg_box.setTextFormat(Qt.RichText)
        msg_box.setText(guide)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    def on_validate_timezone_clicked(self):
        """Validate timezone settings."""
        try:
            timezone = self.ui.timezone_combo.currentText()
            is_valid, result_text, offset = self.app.validate_timezone(timezone)
            
            # Update UI
            self.ui.update_timezone_validation(result_text, is_valid)
            
            # Update timezone information labels
            if self.ui.timezone_status_label:
                self.ui.timezone_status_label.setText(f"Current timezone: {timezone}")
            
            if self.ui.timezone_offset_label and offset is not None:
                self.ui.timezone_offset_label.setText(f"Offset: {offset} minutes")
            
            # If timezone doesn't match, prompt user
            if not is_valid and "System timezone doesn't match selected timezone" in result_text:
                result = QMessageBox.question(
                    self,
                    "Timezone Mismatch",
                    "System timezone doesn't match selected timezone, which may cause browser fingerprint inconsistency.\nView system timezone modification guide?",
                    QMessageBox.Yes | QMessageBox.No
                )
                if result == QMessageBox.Yes:
                    self.show_system_timezone_guide()
            
        except Exception as e:
            self.status_bar.showMessage(f"Timezone validation error: {str(e)}")
            QMessageBox.critical(self, "Timezone Validation Error", f"Error validating timezone settings: {str(e)}")
    
    def on_randomize_clicked(self):
        """Randomize fingerprint settings."""
        try:
            # Randomize based on current IP information if available
            if config.current_ip_info:
                recommended_settings = self.app.apply_fingerprint_from_ip(config.current_ip_info)
                
                # Further randomize some settings
                import random
                if 'resolutions' in recommended_settings:
                    resolutions = ["1920x1080", "1366x768", "2560x1440", "3840x2160", "1280x720"]
                    recommended_settings['screen_resolution'] = random.choice(resolutions)
                
                if 'hardware_concurrency' in recommended_settings:
                    recommended_settings['hardware_concurrency'] = random.randint(2, 16)
                
                # Apply settings to UI
                self.ui.set_all_settings(recommended_settings)
            else:
                # Completely random
                random_settings = self.get_random_settings()
                self.ui.set_all_settings(random_settings)
            
            self.status_bar.showMessage("Randomly generated settings")
            QMessageBox.information(self, "Random Settings", "Randomly generated browser fingerprint settings")
            
        except Exception as e:
            self.status_bar.showMessage(f"Error generating random settings: {str(e)}")
            QMessageBox.critical(self, "Random Settings Error", f"Error generating random settings: {str(e)}")
    
    def get_random_settings(self):
        """Generate completely random settings."""
        import random
        
        # Get random country profile
        country_codes = list(config.country_profiles.keys())
        country_codes.remove('DEFAULT')
        country_code = random.choice(country_codes)
        profile = config.get_country_profile(country_code)
        
        settings = {}
        
        # Random User Agent
        if 'user_agents' in profile and profile['user_agents']:
            settings['user_agent'] = random.choice(profile['user_agents'])
        
        # Random platform
        if 'platforms' in profile and profile['platforms']:
            settings['platform'] = random.choice(profile['platforms'])
        
        # Random resolution
        if 'resolutions' in profile and profile['resolutions']:
            settings['screen_resolution'] = random.choice(profile['resolutions'])
        
        # Random timezone
        if 'timezones' in profile and profile['timezones']:
            settings['timezone'] = random.choice(profile['timezones'])
        
        # Random language
        if 'languages' in profile and profile['languages']:
            settings['language'] = random.choice(profile['languages'])
        
        # Random hardware concurrency
        if 'hardware_concurrency' in profile and profile['hardware_concurrency']:
            settings['hardware_concurrency'] = random.choice(profile['hardware_concurrency'])
        
        # Random device memory
        if 'device_memory' in profile and profile['device_memory']:
            settings['device_memory'] = random.choice(profile['device_memory'])
        
        # Enable all protection features
        settings['canvas_noise'] = True
        settings['webgl_noise'] = True
        settings['audio_noise'] = True
        settings['webrtc_disabled'] = True
        
        return settings
    
    def on_reset_clicked(self):
        """Reset to default settings."""
        self.load_default_settings()
        QMessageBox.information(self, "Reset Settings", "Reset to default system settings")
    
    def on_start_clicked(self):
        """Start fingerprint modification."""
        try:
            # Check admin privileges
            if not config.is_admin():
                result = QMessageBox.warning(
                    self, 
                    "Permission Notice", 
                    "It's recommended to run this program with administrator privileges for full functionality.\nSome system-level modifications may not apply in current mode.\nContinue anyway?",
                    QMessageBox.Yes | QMessageBox.No
                )
                if result == QMessageBox.No:
                    return
            
            # Get current settings
            settings = self.ui.get_all_settings()
            
            # Start fingerprint modification
            success = self.app.start_spoofing(settings)
            
            if success:
                self.ui.global_start_btn.setEnabled(False)
                self.status_bar.showMessage("Applying fingerprint modifications...")
            else:
                QMessageBox.critical(self, "Startup Error", "Could not start fingerprint modification")
                
        except Exception as e:
            self.status_bar.showMessage(f"Startup error: {str(e)}")
            QMessageBox.critical(self, "Startup Error", f"Could not start fingerprint modification: {str(e)}")
    
    def on_spoofing_finished(self, success, message):
        """Handle fingerprint modification completion."""
        self.ui.global_start_btn.setEnabled(True)
        
        if success:
            self.ui.enable_restore_button(True)
            QMessageBox.information(self, "Startup Success", "Fingerprint modifications successfully applied and running")
        else:
            QMessageBox.critical(self, "Application Error", message)
    
    def on_restore_clicked(self):
        """Restore original system environment."""
        try:
            # Confirm restoration
            result = QMessageBox.question(
                self,
                "Confirm Restoration",
                "Are you sure you want to restore the original system environment?",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if result == QMessageBox.No:
                return
            
            # Start restoration
            success = self.app.restore_environment()
            
            if success:
                self.ui.enable_restore_button(False)
                self.status_bar.showMessage("Restoring original system environment...")
            else:
                QMessageBox.critical(self, "Restoration Error", "Could not start restoration process")
                
        except Exception as e:
            self.status_bar.showMessage(f"Environment restoration error: {str(e)}")
            QMessageBox.critical(self, "Restoration Error", f"Could not restore original system environment: {str(e)}")
    
    def on_restore_finished(self, success, message):
        """Handle environment restoration completion."""
        self.ui.enable_restore_button(True)
        
        if success:
            QMessageBox.information(self, "Restoration Success", "Restored to original system environment")
        else:
            QMessageBox.critical(self, "Restoration Error", message)
    
    def on_launch_chrome_clicked(self):
        """Launch Chrome sandbox environment."""
        try:
            # Get current settings
            settings = self.ui.get_all_settings()
            
            # Ensure chrome_sandbox directory exists and is clean
            sandbox_dir = os.path.join(config.config_dir, "chrome_sandbox")
            temp_dir = os.path.join(sandbox_dir, f"session_{time.strftime('%Y%m%d%H%M%S')}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # Save current settings to JSON for debugging
            settings_file = os.path.join(temp_dir, "settings.json")
            with open(settings_file, "w", encoding="utf-8") as f:
                json.dump(settings, f, indent=2)
            
            # Launch Chrome sandbox
            success, message = self.app.launch_chrome_sandbox(settings)
            
            if success:
                QMessageBox.information(self, "Launch Success", message)
            else:
                QMessageBox.critical(self, "Launch Error", message)
                
        except Exception as e:
            self.status_bar.showMessage(f"Chrome sandbox launch error: {str(e)}")
            QMessageBox.critical(self, "Launch Error", f"Could not launch Chrome sandbox: {str(e)}")
    
    def on_clean_cookies_clicked(self):
        """Clean browser cookies and cache."""
        try:
            # Confirm cleaning
            result = QMessageBox.question(
                self,
                "Confirm Cleaning",
                "Are you sure you want to clean all browser cookies and cache? Account-related cookies will be preserved.",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if result == QMessageBox.No:
                return
            
            # Clean cookies and cache
            success, message = self.app.clean_cookies_cache()
            
            if success:
                QMessageBox.information(self, "Cleaning Complete", message)
            else:
                QMessageBox.warning(self, "Cleaning Failed", message)
                
        except Exception as e:
            self.status_bar.showMessage(f"Error cleaning cookies and cache: {str(e)}")
            QMessageBox.critical(self, "Cleaning Error", f"Error cleaning cookies and cache: {str(e)}")
    
    def on_save_clicked(self):
        """Save current settings to configuration file."""
        try:
            # Get current settings
            settings = self.ui.get_all_settings()
            
            # Save settings
            success, message = self.app.save_settings(settings)
            
            if success:
                QMessageBox.information(self, "Save Success", message)
            else:
                QMessageBox.critical(self, "Save Error", message)
                
        except Exception as e:
            self.status_bar.showMessage(f"Error saving settings: {str(e)}")
            QMessageBox.critical(self, "Save Error", f"Could not save settings: {str(e)}")

"""
设备配置文件系统 - 提供完整的设备特征库
"""

# 设备配置数据结构
DEVICE_PROFILES = {
    # Windows 设备
    "windows_10_chrome": {
        "name": "Windows 10 - Chrome",
        "category": "桌面",
        "platform": "Windows",
        "os_version": "10.0",
        "architecture": "x64",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "1920x1080",
        "color_depth": 24,
        "pixel_depth": 24,
        "hardware_concurrency": 8,
        "device_memory": 8,
        "platform_details": "Win32",
        "oscpu": "Windows NT 10.0; Win64; x64",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Google Inc.",
        "webgl_vendor": "Google Inc. (NVIDIA)",
        "webgl_renderer": "ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, D3D11)",
        "touch_points": 0,
        "font_list": "Arial,Arial Black,Arial Narrow,Calibri,Cambria,Cambria Math,Comic Sans MS,Consolas,Courier,Courier New,Georgia,Helvetica,Impact,Lucida Console,Lucida Sans Unicode,Microsoft Sans Serif,MS Gothic,MS PGothic,MS Sans Serif,MS Serif,Palatino Linotype,Segoe UI,Segoe UI Light,Segoe UI Semibold,Segoe UI Symbol,Tahoma,Times,Times New Roman,Trebuchet MS,Verdana,Wingdings",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": ["PDF Viewer", "Chrome PDF Viewer", "Chromium PDF Viewer"],
        "mime_types": ["application/pdf", "text/pdf"],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "NVIDIA",
        "gpu_renderer": "GeForce RTX 3060"
    },

    "windows_10_edge": {
        "name": "Windows 10 - Edge",
        "category": "桌面",
        "platform": "Windows",
        "os_version": "10.0",
        "architecture": "x64",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "1920x1080",
        "color_depth": 24,
        "pixel_depth": 24,
        "hardware_concurrency": 8,
        "device_memory": 8,
        "platform_details": "Win32",
        "oscpu": "Windows NT 10.0; Win64; x64",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Microsoft",
        "webgl_vendor": "Google Inc. (NVIDIA)",
        "webgl_renderer": "ANGLE (NVIDIA, NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0, D3D11)",
        "touch_points": 0,
        "font_list": "Arial,Arial Black,Arial Narrow,Calibri,Cambria,Cambria Math,Comic Sans MS,Consolas,Courier,Courier New,Georgia,Helvetica,Impact,Lucida Console,Lucida Sans Unicode,Microsoft Sans Serif,MS Gothic,MS PGothic,MS Sans Serif,MS Serif,Palatino Linotype,Segoe UI,Segoe UI Light,Segoe UI Semibold,Segoe UI Symbol,Tahoma,Times,Times New Roman,Trebuchet MS,Verdana,Wingdings",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": ["PDF Viewer", "Microsoft Edge PDF Viewer"],
        "mime_types": ["application/pdf", "text/pdf"],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "NVIDIA",
        "gpu_renderer": "GeForce RTX 3060"
    },

    "windows_11_chrome": {
        "name": "Windows 11 - Chrome",
        "category": "桌面",
        "platform": "Windows",
        "os_version": "10.0",
        "architecture": "x64",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "2560x1440",
        "color_depth": 24,
        "pixel_depth": 24,
        "hardware_concurrency": 12,
        "device_memory": 16,
        "platform_details": "Win32",
        "oscpu": "Windows NT 10.0; Win64; x64",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Google Inc.",
        "webgl_vendor": "Google Inc. (NVIDIA)",
        "webgl_renderer": "ANGLE (NVIDIA, NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0, D3D11)",
        "touch_points": 0,
        "font_list": "Arial,Arial Black,Arial Narrow,Calibri,Cambria,Cambria Math,Comic Sans MS,Consolas,Courier,Courier New,Georgia,Helvetica,Impact,Lucida Console,Lucida Sans Unicode,Microsoft Sans Serif,MS Gothic,MS PGothic,MS Sans Serif,MS Serif,Palatino Linotype,Segoe UI,Segoe UI Light,Segoe UI Semibold,Segoe UI Symbol,Segoe UI Variable,Tahoma,Times,Times New Roman,Trebuchet MS,Verdana,Wingdings",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": ["PDF Viewer", "Chrome PDF Viewer", "Chromium PDF Viewer"],
        "mime_types": ["application/pdf", "text/pdf"],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "NVIDIA",
        "gpu_renderer": "GeForce RTX 4070"
    },

    # macOS 设备
    "macos_safari": {
        "name": "macOS - Safari",
        "category": "桌面",
        "platform": "MacIntel",
        "os_version": "10.15.7",
        "architecture": "x64",
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
        "language": "zh-cn",
        "languages": ["zh-cn", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "2560x1600",
        "color_depth": 30,
        "pixel_depth": 30,
        "hardware_concurrency": 10,
        "device_memory": 16,
        "platform_details": "MacIntel",
        "oscpu": "Intel Mac OS X 10_15_7",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Apple Computer, Inc.",
        "webgl_vendor": "Apple Inc.",
        "webgl_renderer": "Apple M1 Pro",
        "touch_points": 0,
        "font_list": "American Typewriter,Andale Mono,Arial,Arial Black,Arial Narrow,Arial Rounded MT Bold,Arial Unicode MS,Avenir,Avenir Next,Avenir Next Condensed,Baskerville,Big Caslon,Bodoni 72,Bodoni 72 Oldstyle,Bodoni 72 Smallcaps,Bradley Hand,Brush Script MT,Chalkboard,Chalkboard SE,Chalkduster,Charter,Cochin,Comic Sans MS,Copperplate,Courier,Courier New,Didot,DIN Alternate,DIN Condensed,Futura,Geneva,Georgia,Gill Sans,Helvetica,Helvetica Neue,Herculanum,Hoefler Text,Impact,Lucida Grande,Luminari,Marker Felt,Menlo,Microsoft Sans Serif,Monaco,Noteworthy,Optima,Palatino,Papyrus,Phosphate,Rockwell,SF Pro,Tahoma,Times,Times New Roman,Trebuchet MS,Verdana,Zapfino",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": ["PDF Viewer", "WebKit built-in PDF"],
        "mime_types": ["application/pdf", "text/pdf"],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "Apple",
        "gpu_renderer": "Apple M1 Pro"
    },

    "macos_chrome": {
        "name": "macOS - Chrome",
        "category": "桌面",
        "platform": "MacIntel",
        "os_version": "10.15.7",
        "architecture": "x64",
        "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "2560x1600",
        "color_depth": 30,
        "pixel_depth": 30,
        "hardware_concurrency": 10,
        "device_memory": 16,
        "platform_details": "MacIntel",
        "oscpu": "Intel Mac OS X 10_15_7",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Google Inc.",
        "webgl_vendor": "Apple Inc.",
        "webgl_renderer": "Apple M1 Pro",
        "touch_points": 0,
        "font_list": "American Typewriter,Andale Mono,Arial,Arial Black,Arial Narrow,Arial Rounded MT Bold,Arial Unicode MS,Avenir,Avenir Next,Avenir Next Condensed,Baskerville,Big Caslon,Bodoni 72,Bodoni 72 Oldstyle,Bodoni 72 Smallcaps,Bradley Hand,Brush Script MT,Chalkboard,Chalkboard SE,Chalkduster,Charter,Cochin,Comic Sans MS,Copperplate,Courier,Courier New,Didot,DIN Alternate,DIN Condensed,Futura,Geneva,Georgia,Gill Sans,Helvetica,Helvetica Neue,Herculanum,Hoefler Text,Impact,Lucida Grande,Luminari,Marker Felt,Menlo,Microsoft Sans Serif,Monaco,Noteworthy,Optima,Palatino,Papyrus,Phosphate,Rockwell,SF Pro,Tahoma,Times,Times New Roman,Trebuchet MS,Verdana,Zapfino",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": ["PDF Viewer", "Chrome PDF Viewer", "Chromium PDF Viewer"],
        "mime_types": ["application/pdf", "text/pdf"],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "Apple",
        "gpu_renderer": "Apple M1 Pro"
    },

    # 安卓设备
    "android_chrome": {
        "name": "Android - Chrome Mobile",
        "category": "移动",
        "platform": "Android",
        "os_version": "13.0",
        "architecture": "arm64",
        "user_agent": "Mozilla/5.0 (Linux; Android 13; SM-S901B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Mobile Safari/537.36",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "412x915",
        "color_depth": 24,
        "pixel_depth": 24,
        "hardware_concurrency": 8,
        "device_memory": 4,
        "platform_details": "Linux aarch64",
        "oscpu": "Linux; Android 13",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Google Inc.",
        "webgl_vendor": "Google Inc.",
        "webgl_renderer": "Adreno (TM) 730",
        "touch_points": 5,
        "font_list": "Roboto,Noto Sans,Noto Sans JP,Noto Sans KR,Noto Naskh Arabic,Noto Sans Thai,Noto Sans Hebrew,Noto Sans Bengali,Droid Sans,Droid Sans Fallback",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": [],
        "mime_types": [],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "Qualcomm",
        "gpu_renderer": "Adreno 730"
    },

    "android_samsung": {
        "name": "Android - Samsung Browser",
        "category": "移动",
        "platform": "Android",
        "os_version": "13.0",
        "architecture": "arm64",
        "user_agent": "Mozilla/5.0 (Linux; Android 13; SM-S901B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/21.0 Chrome/110.0.5481.154 Mobile Safari/537.36",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "412x915",
        "color_depth": 24,
        "pixel_depth": 24,
        "hardware_concurrency": 8,
        "device_memory": 4,
        "platform_details": "Linux aarch64",
        "oscpu": "Linux; Android 13",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Google Inc.",
        "webgl_vendor": "Google Inc.",
        "webgl_renderer": "Adreno (TM) 730",
        "touch_points": 5,
        "font_list": "Roboto,Noto Sans,Samsung Sans,SamsungOneUI,SamsungKorean,Noto Sans JP,Noto Sans KR,Noto Naskh Arabic,Noto Sans Thai,Noto Sans Hebrew,Noto Sans Bengali",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": [],
        "mime_types": [],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "Qualcomm",
        "gpu_renderer": "Adreno 730"
    },

    "android_xiaomi": {
        "name": "Android - Xiaomi MIUI Browser",
        "category": "移动",
        "platform": "Android",
        "os_version": "13.0",
        "architecture": "arm64",
        "user_agent": "Mozilla/5.0 (Linux; Android 13; 22081212C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "393x873",
        "color_depth": 24,
        "pixel_depth": 24,
        "hardware_concurrency": 8,
        "device_memory": 4,
        "platform_details": "Linux aarch64",
        "oscpu": "Linux; Android 13",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Google Inc.",
        "webgl_vendor": "Google Inc.",
        "webgl_renderer": "Adreno (TM) 730",
        "touch_points": 5,
        "font_list": "Roboto,MiSans,Mi Lan Pro,Noto Sans,Noto Sans JP,Noto Sans KR,Noto Naskh Arabic,Noto Sans Thai,Noto Sans Hebrew,Noto Sans Bengali",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": [],
        "mime_types": [],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "Qualcomm",
        "gpu_renderer": "Adreno 730"
    },

    # iPhone设备
    "iphone_safari": {
        "name": "iPhone - Safari",
        "category": "移动",
        "platform": "iPhone",
        "os_version": "17.2",
        "architecture": "arm64",
        "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
        "language": "zh-cn",
        "languages": ["zh-cn", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "390x844",
        "color_depth": 32,
        "pixel_depth": 32,
        "hardware_concurrency": 6,
        "device_memory": None,  # iOS不支持deviceMemory API
        "platform_details": "iPhone",
        "oscpu": "CPU iPhone OS 17_2 like Mac OS X",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Apple Computer, Inc.",
        "webgl_vendor": "Apple Inc.",
        "webgl_renderer": "Apple GPU",
        "touch_points": 5,
        "font_list": "Academy Engraved LET,Al Nile,American Typewriter,Apple Color Emoji,Apple SD Gothic Neo,Arial,Arial Hebrew,Arial Rounded MT Bold,Avenir,Avenir Next,Avenir Next Condensed,Baskerville,Bodoni 72,Bradley Hand,Chalkboard SE,Cochin,Copperplate,Courier,Courier New,Damascus,Devanagari Sangam MN,Didot,DIN Alternate,DIN Condensed,Futura,Geneva,Georgia,Gill Sans,Helvetica,Helvetica Neue,Hiragino Sans,Hoefler Text,Kailasa,Menlo,Noteworthy,Optima,Palatino,Papyrus,Party LET,San Francisco,Savoye LET,SignPainter,Snell Roundhand,Symbol,Thonburi,Times New Roman,Trebuchet MS,Verdana,Zapf Dingbats",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": [],
        "mime_types": [],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "Apple",
        "gpu_renderer": "Apple GPU"
    },
    
    # Linux设备
    "linux_chrome": {
        "name": "Linux - Chrome",
        "category": "桌面",
        "platform": "Linux",
        "os_version": "5.15.0",
        "architecture": "x64",
        "user_agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "1920x1080",
        "color_depth": 24,
        "pixel_depth": 24,
        "hardware_concurrency": 8,
        "device_memory": 8,
        "platform_details": "Linux x86_64",
        "oscpu": "Linux x86_64",
        "product": "Gecko",
        "product_sub": "20030107",
        "vendor": "Google Inc.",
        "webgl_vendor": "Google Inc. (Intel)",
        "webgl_renderer": "ANGLE (Intel, Mesa Intel(R) UHD Graphics 620 (CFL GT2), OpenGL 4.6)",
        "touch_points": 0,
        "font_list": "Bitstream Vera Sans,DejaVu Sans,DejaVu Sans Mono,DejaVu Serif,Droid Sans,Droid Sans Fallback,FreeMono,FreeSans,FreeSerif,Liberation Mono,Liberation Sans,Liberation Serif,Noto Color Emoji,Noto Sans,Noto Serif,Ubuntu,Ubuntu Mono",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": ["PDF Viewer", "Chrome PDF Viewer", "Chromium PDF Viewer"],
        "mime_types": ["application/pdf", "text/pdf"],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "Intel",
        "gpu_renderer": "Mesa Intel UHD Graphics 620"
    },

    "linux_firefox": {
        "name": "Linux - Firefox",
        "category": "桌面",
        "platform": "Linux",
        "os_version": "5.15.0",
        "architecture": "x64",
        "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "language": "zh-CN",
        "languages": ["zh-CN", "zh", "en-US", "en"],
        "timezone": "Asia/Shanghai",
        "screen_resolution": "1920x1080",
        "color_depth": 24,
        "pixel_depth": 24,
        "hardware_concurrency": 8,
        "device_memory": 8,
        "platform_details": "Linux x86_64",
        "oscpu": "Linux x86_64",
        "product": "Gecko",
        "product_sub": "20100101",
        "vendor": "",
        "webgl_vendor": "Intel Inc.",
        "webgl_renderer": "Mesa Intel(R) UHD Graphics 620 (CFL GT2)",
        "touch_points": 0,
        "font_list": "Bitstream Vera Sans,DejaVu Sans,DejaVu Sans Mono,DejaVu Serif,Droid Sans,Droid Sans Fallback,FreeMono,FreeSans,FreeSerif,Liberation Mono,Liberation Sans,Liberation Serif,Noto Color Emoji,Noto Sans,Noto Serif,Ubuntu,Ubuntu Mono",
        "canvas_fingerprint_mode": "noise",
        "webgl_fingerprint_mode": "consistent",
        "audio_fingerprint_mode": "noise",
        "webrtc_fingerprint_mode": "proxy",
        "canvas_noise_level": 1.5,
        "plugins": [],
        "mime_types": ["application/pdf"],
        "webgl_extensions": [
            "ANGLE_instanced_arrays", "EXT_blend_minmax", "EXT_color_buffer_half_float",
            "EXT_float_blend", "EXT_frag_depth", "EXT_shader_texture_lod", "EXT_texture_filter_anisotropic",
            "OES_element_index_uint", "OES_standard_derivatives", "OES_texture_float",
            "OES_texture_float_linear", "OES_texture_half_float", "OES_texture_half_float_linear",
            "WEBGL_compressed_texture_s3tc", "WEBGL_depth_texture", "WEBGL_lose_context"
        ],
        "gpu_vendor": "Intel",
        "gpu_renderer": "Mesa Intel UHD Graphics 620"
    }
}

# 按类别组织配置文件
DEVICE_CATEGORIES = {
    "桌面": [
        "windows_10_chrome",
        "windows_10_edge",
        "windows_11_chrome",
        "macos_safari",
        "macos_chrome",
        "linux_chrome",
        "linux_firefox"
    ],
    "移动": [
        "android_chrome",
        "android_samsung",
        "android_xiaomi",
        "iphone_safari"
    ]
}

def get_device_profile(profile_id):
    """获取指定ID的设备配置文件"""
    return DEVICE_PROFILES.get(profile_id)

def get_all_device_profiles():
    """获取所有设备配置文件"""
    return DEVICE_PROFILES

def get_device_categories():
    """获取设备类别列表"""
    return DEVICE_CATEGORIES

def convert_profile_to_settings(profile):
    """将配置文件转换为settings格式"""
    settings = {
        'user_agent': profile['user_agent'],
        'platform': profile['platform'],
        'screen_resolution': profile['screen_resolution'],
        'language': profile['language'],
        'timezone': profile['timezone'],
        'hardware_concurrency': profile['hardware_concurrency'],
        'device_memory': profile['device_memory'] if profile['device_memory'] is not None else 4,
        'font_list': profile['font_list'],
        'canvas_noise': profile['canvas_fingerprint_mode'] != 'disabled',
        'webgl_noise': profile['webgl_fingerprint_mode'] != 'disabled',
        'audio_noise': profile['audio_fingerprint_mode'] != 'disabled',
        'webrtc_disabled': profile['webrtc_fingerprint_mode'] in ['proxy', 'disabled'],
        'webgl_vendor': profile['webgl_vendor'],
        'webgl_renderer': profile['webgl_renderer'],
        'device_profile_id': profile.get('id', None)
    }
    return settings
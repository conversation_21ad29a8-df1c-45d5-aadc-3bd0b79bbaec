import os
import platform
import ctypes
import json

# 设置配置目录 - 使用当前目录而非AppData
config_dir = os.path.join(os.getcwd(), "fingerprint_config")
if not os.path.exists(config_dir):
    try:
        os.makedirs(config_dir)
    except Exception as e:
        print(f"创建配置目录错误: {str(e)}")

# 存储当前IP信息的全局变量
current_ip_info = {}

# 当前的时区偏移量
current_timezone_offset = 0

# 时区映射表 - 用于验证和设置时区偏移
timezone_map = {
    # 非洲
    "Africa/Cairo": -120,           # UTC+2
    "Africa/Johannesburg": -120,    # UTC+2
    "Africa/Lagos": -60,            # UTC+1
    # 美洲
    "America/Anchorage": 540,       # UTC-9
    "America/Bogota": 300,          # UTC-5
    "America/Chicago": 360,         # UTC-6
    "America/Denver": 420,          # UTC-7
    "America/Los_Angeles": 480,     # UTC-8
    "America/Mexico_City": 360,     # UTC-6
    "America/New_York": 300,        # UTC-5
    "America/Phoenix": 420,         # UTC-7
    "America/Sao_Paulo": 180,       # UTC-3
    "America/Toronto": 300,         # UTC-5
    "America/Vancouver": 480,       # UTC-8
    # 亚洲
    "Asia/Bangkok": -420,           # UTC+7
    "Asia/Dubai": -240,             # UTC+4
    "Asia/Hong_Kong": -480,         # UTC+8
    "Asia/Jakarta": -420,           # UTC+7
    "Asia/Kolkata": -330,           # UTC+5:30
    "Asia/Seoul": -540,             # UTC+9
    "Asia/Shanghai": -480,          # UTC+8
    "Asia/Singapore": -480,         # UTC+8
    "Asia/Tokyo": -540,             # UTC+9
    # 澳大利亚和太平洋
    "Australia/Melbourne": -600,    # UTC+10
    "Australia/Perth": -480,        # UTC+8
    "Australia/Sydney": -600,       # UTC+10
    # 欧洲
    "Europe/Amsterdam": -60,        # UTC+1
    "Europe/Berlin": -60,           # UTC+1
    "Europe/Istanbul": -180,        # UTC+3
    "Europe/London": 0,             # UTC+0
    "Europe/Madrid": -60,           # UTC+1
    "Europe/Moscow": -180,          # UTC+3
    "Europe/Paris": -60,            # UTC+1
    "Europe/Rome": -60,             # UTC+1
    "Europe/Stockholm": -60,        # UTC+1
    "Europe/Zurich": -60,           # UTC+1
    # 太平洋
    "Pacific/Auckland": -720,       # UTC+12
    "Pacific/Honolulu": 600         # UTC-10
}

# 常见国家特征配置
country_profiles = {
    # 中国
    'CN': {
        'user_agents': [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        ],
        'platforms': ["Windows", "Windows NT 10.0"],
        'languages': ["zh-CN"],
        'timezones': ["Asia/Shanghai", "Asia/Hong_Kong", "Asia/Urumqi"],
        'resolutions': ["1920x1080", "1366x768", "2560x1440", "3840x2160", "1280x720"],
        'fonts': "Arial,Microsoft YaHei,SimSun,SimHei,NSimSun,FangSong,KaiTi,PingFang SC,Heiti SC,STHeiti,STKaiti,STSong,STFangsong,Hiragino Sans GB,Source Han Sans CN,WenQuanYi Micro Hei",
        'hardware_concurrency': [4, 8, 12, 16],
        'device_memory': [4, 8, 16, 32]
    },
    # 美国
    'US': {
        'user_agents': [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        ],
        'platforms': ["Windows", "MacIntel"],
        'languages': ["en-US"],
        'timezones': ["America/New_York", "America/Chicago", "America/Los_Angeles", "America/Denver", "America/Phoenix"],
        'resolutions': ["1920x1080", "2560x1440", "3840x2160", "1366x768", "1440x900"],
        'fonts': "Arial,Helvetica,Times New Roman,Verdana,Georgia,Tahoma,Trebuchet MS,Arial Black,Impact,Palatino Linotype,Courier New,Lucida Sans Unicode,Lucida Console,Comic Sans MS,Segoe UI",
        'hardware_concurrency': [4, 8, 12, 16],
        'device_memory': [8, 16, 32]
    },
    # 日本
    'JP': {
        'user_agents': [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
        ],
        'platforms': ["Windows", "MacIntel", "iPhone"],
        'languages': ["ja-JP"],
        'timezones': ["Asia/Tokyo"],
        'resolutions': ["1920x1080", "1366x768", "2560x1440", "390x844"],
        'fonts': "Arial,Helvetica,Times New Roman,Meiryo,MS PGothic,MS Gothic,Yu Gothic,Hiragino Kaku Gothic Pro,Hiragino Mincho Pro,MS Mincho,Osaka,Tahoma",
        'hardware_concurrency': [4, 8, 12, 16],
        'device_memory': [8, 16, 32]
    },
    # 韩国
    'KR': {
        'user_agents': [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        ],
        'platforms': ["Windows"],
        'languages': ["ko-KR"],
        'timezones': ["Asia/Seoul"],
        'resolutions': ["1920x1080", "2560x1440", "3840x2160"],
        'fonts': "Arial,Helvetica,Times New Roman,Malgun Gothic,Batang,Dotum,Gulim,KoPubWorld Dotum,Nanum Gothic,Nanum Myeongjo",
        'hardware_concurrency': [4, 8, 12, 16],
        'device_memory': [8, 16, 32]
    },
    # 默认配置(如果无法找到国家特定配置)
    'DEFAULT': {
        'user_agents': [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ],
        'platforms': ["Windows"],
        'languages': ["en-US"],
        'timezones': ["America/New_York"],
        'resolutions': ["1920x1080"],
        'fonts': "Arial, Helvetica, Times New Roman",
        'hardware_concurrency': [4, 8],
        'device_memory': [8]
    }
}

def get_country_profile(country_code):
    """根据国家代码获取对应的浏览器特征配置"""
    if country_code in country_profiles:
        return country_profiles[country_code]
    return country_profiles['DEFAULT']

def is_admin():
    """检查程序是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin() == 1
    except:
        return False

def save_settings(settings, filepath=None):
    """保存设置到配置文件"""
    if filepath is None:
        filepath = os.path.join(config_dir, "config.json")
    
    try:
        with open(filepath, "w") as f:
            json.dump(settings, f, indent=4)
        return True, f"设置已保存到 {filepath}"
    except Exception as e:
        return False, f"保存设置错误: {str(e)}"

def load_settings(filepath=None):
    """从配置文件加载设置"""
    if filepath is None:
        filepath = os.path.join(config_dir, "config.json")
    
    if not os.path.exists(filepath):
        return None
    
    try:
        with open(filepath, "r") as f:
            return json.load(f)
    except Exception as e:
        print(f"加载设置错误: {str(e)}")
        return None

# 创建config对象，包含所有配置变量
class Config:
    def __init__(self):
        self.config_dir = config_dir
        self.current_ip_info = current_ip_info
        self.current_timezone_offset = current_timezone_offset
        self.timezone_map = timezone_map
        self.country_profiles = country_profiles
        
    def get_country_profile(self, country_code):
        return get_country_profile(country_code)
        
    def is_admin(self):
        return is_admin()
        
    def save_settings(self, settings, filepath=None):
        return save_settings(settings, filepath)
        
    def load_settings(self, filepath=None):
        return load_settings(filepath)

# 创建config实例供其他模块导入
config = Config()
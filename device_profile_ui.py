"""
设备配置UI面板 - 为指纹修改器添加高级设备配置选择
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox, 
    QLabel, QPushButton, QGroupBox, QScrollArea,
    QTabWidget, QFormLayout, QLineEdit, QCheckBox,
    QFrame, QSplitter, QSpinBox, QDoubleSpinBox, 
    QMessageBox, QListWidget, QListWidgetItem, QGridLayout
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from device_profiles import (
    get_device_profile,
    get_all_device_profiles,
    get_device_categories,
    convert_profile_to_settings
)

class DeviceProfileUI(QWidget):
    """设备配置选择UI面板"""
    
    # 信号定义
    profile_selected = pyqtSignal(dict)  # 发送选择的配置
    apply_profile = pyqtSignal(dict)     # 应用配置
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.current_profile = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建设备选择区域
        selection_group = QGroupBox("设备配置选择")
        selection_layout = QVBoxLayout(selection_group)
        
        # 设备类别选择
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("设备类别:"))
        self.category_combo = QComboBox()
        self.category_combo.addItems(list(get_device_categories().keys()))
        self.category_combo.currentIndexChanged.connect(self.on_category_changed)
        category_layout.addWidget(self.category_combo)
        selection_layout.addLayout(category_layout)
        
        # 设备列表
        self.device_list = QListWidget()
        self.device_list.setStyleSheet("QListWidget{min-height: 150px;}")
        self.device_list.currentItemChanged.connect(self.on_device_selected)
        selection_layout.addWidget(self.device_list)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.apply_btn = QPushButton("应用此设备配置")
        self.apply_btn.clicked.connect(self.on_apply_clicked)
        self.apply_btn.setEnabled(False)
        buttons_layout.addWidget(self.apply_btn)
        
        self.modify_btn = QPushButton("自定义配置...")
        self.modify_btn.clicked.connect(self.on_modify_clicked)
        self.modify_btn.setEnabled(False)
        buttons_layout.addWidget(self.modify_btn)
        
        selection_layout.addLayout(buttons_layout)
        
        # 添加到主布局
        main_layout.addWidget(selection_group)
        
        # 配置详情区域
        details_group = QGroupBox("配置详情")
        details_layout = QVBoxLayout(details_group)
        
        # 使用滚动区域显示详情
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        details_content = QWidget()
        self.details_form = QFormLayout(details_content)
        self.details_form.setVerticalSpacing(8)
        self.details_form.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        
        # 创建所有详情字段
        self._create_detail_fields()
        
        scroll_area.setWidget(details_content)
        details_layout.addWidget(scroll_area)
        
        # 添加到主布局
        main_layout.addWidget(details_group)
        
        # 加载初始数据
        self.on_category_changed(0)
    
    def _create_detail_fields(self):
        """创建详情显示字段"""
        # 基本信息
        self.detail_fields = {}
        
        # 设备名称
        name_label = QLabel()
        name_label.setStyleSheet("font-weight: bold; color: #0056b3;")
        self.detail_fields['name'] = name_label
        self.details_form.addRow("设备名称:", name_label)
        
        # 平台
        platform_label = QLabel()
        self.detail_fields['platform'] = platform_label
        self.details_form.addRow("平台:", platform_label)
        
        # 操作系统
        os_label = QLabel()
        self.detail_fields['os'] = os_label
        self.details_form.addRow("操作系统:", os_label)
        
        # User Agent
        ua_label = QLabel()
        ua_label.setWordWrap(True)
        self.detail_fields['user_agent'] = ua_label
        self.details_form.addRow("User Agent:", ua_label)
        
        # 分辨率
        resolution_label = QLabel()
        self.detail_fields['resolution'] = resolution_label
        self.details_form.addRow("分辨率:", resolution_label)
        
        # 语言
        language_label = QLabel()
        self.detail_fields['language'] = language_label
        self.details_form.addRow("语言:", language_label)
        
        # 时区
        timezone_label = QLabel()
        self.detail_fields['timezone'] = timezone_label
        self.details_form.addRow("时区:", timezone_label)
        
        # 硬件信息
        hardware_label = QLabel()
        self.detail_fields['hardware'] = hardware_label
        self.details_form.addRow("硬件信息:", hardware_label)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        self.details_form.addRow(line)
        
        # WebGL信息
        webgl_label = QLabel()
        webgl_label.setWordWrap(True)
        self.detail_fields['webgl'] = webgl_label
        self.details_form.addRow("WebGL信息:", webgl_label)
        
        # 字体列表
        font_label = QLabel()
        font_label.setWordWrap(True)
        self.detail_fields['fonts'] = font_label
        self.details_form.addRow("字体列表:", font_label)
        
        # 分隔线
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setFrameShadow(QFrame.Sunken)
        self.details_form.addRow(line2)
        
        # 指纹保护设置
        protection_label = QLabel()
        self.detail_fields['protection'] = protection_label
        self.details_form.addRow("指纹保护:", protection_label)
    
    def on_category_changed(self, index):
        """设备类别改变时更新设备列表"""
        self.device_list.clear()
        
        category = self.category_combo.currentText()
        device_ids = get_device_categories().get(category, [])
        
        all_profiles = get_all_device_profiles()
        
        for device_id in device_ids:
            profile = all_profiles.get(device_id)
            if profile:
                item = QListWidgetItem(profile['name'])
                item.setData(Qt.UserRole, device_id)
                self.device_list.addItem(item)
    
    def on_device_selected(self, current, previous):
        """设备选择改变时更新详情"""
        if not current:
            self.current_profile = None
            self.clear_details()
            self.apply_btn.setEnabled(False)
            self.modify_btn.setEnabled(False)
            return
        
        device_id = current.data(Qt.UserRole)
        profile = get_device_profile(device_id)
        
        if not profile:
            self.current_profile = None
            self.clear_details()
            self.apply_btn.setEnabled(False)
            self.modify_btn.setEnabled(False)
            return
        
        # 保存当前配置
        self.current_profile = profile
        
        # 更新UI
        self.update_details(profile)
        self.apply_btn.setEnabled(True)
        self.modify_btn.setEnabled(True)
        
        # 发送信号
        self.profile_selected.emit(profile)
    
    def on_apply_clicked(self):
        """应用按钮点击事件"""
        if not self.current_profile:
            return
        
        # 转换为settings格式
        settings = convert_profile_to_settings(self.current_profile)
        
        # 发送信号
        self.apply_profile.emit(settings)
        
        # 显示确认信息
        QMessageBox.information(
            self,
            "配置已应用",
            f"已成功应用'{self.current_profile['name']}'设备配置"
        )
    
    def on_modify_clicked(self):
        """修改按钮点击事件"""
        if not self.current_profile:
            return
        
        # 在这里可以打开一个编辑对话框
        QMessageBox.information(
            self,
            "即将推出",
            "自定义配置功能即将推出，敬请期待！"
        )
    
    def update_details(self, profile):
        """更新详情显示"""
        # 基本信息
        self.detail_fields['name'].setText(profile['name'])
        self.detail_fields['platform'].setText(f"{profile['platform']}")
        self.detail_fields['os'].setText(f"{profile['platform']} {profile['os_version']} ({profile['architecture']})")
        self.detail_fields['user_agent'].setText(profile['user_agent'])
        self.detail_fields['resolution'].setText(f"{profile['screen_resolution']} ({profile['color_depth']}位色深)")
        self.detail_fields['language'].setText(f"{profile['language']}")
        self.detail_fields['timezone'].setText(f"{profile['timezone']}")
        
        # 硬件信息
        hw_info = f"CPU核心: {profile['hardware_concurrency']}"
        if profile['device_memory'] is not None:
            hw_info += f", 内存: {profile['device_memory']}GB"
        if profile['touch_points'] > 0:
            hw_info += f", 触控点: {profile['touch_points']}"
        self.detail_fields['hardware'].setText(hw_info)
        
        # WebGL信息
        webgl_info = f"{profile['webgl_vendor']}, {profile['webgl_renderer']}"
        self.detail_fields['webgl'].setText(webgl_info)
        
        # 字体列表
        fonts = profile['font_list'].split(',')
        if len(fonts) > 5:
            fonts_text = ", ".join(fonts[:5]) + "... 等" + str(len(fonts)) + "种字体"
        else:
            fonts_text = profile['font_list']
        self.detail_fields['fonts'].setText(fonts_text)
        
        # 指纹保护设置
        protection = []
        if profile['canvas_fingerprint_mode'] != 'disabled':
            protection.append(f"Canvas ({profile['canvas_fingerprint_mode']})")
        if profile['webgl_fingerprint_mode'] != 'disabled':
            protection.append(f"WebGL ({profile['webgl_fingerprint_mode']})")
        if profile['audio_fingerprint_mode'] != 'disabled':
            protection.append(f"音频 ({profile['audio_fingerprint_mode']})")
        if profile['webrtc_fingerprint_mode'] != 'disabled':
            protection.append(f"WebRTC ({profile['webrtc_fingerprint_mode']})")
        
        self.detail_fields['protection'].setText(", ".join(protection))
    
    def clear_details(self):
        """清空详情显示"""
        for field in self.detail_fields.values():
            field.setText("")

def create_device_tab(parent):
    """创建设备标签页"""
    tab = QWidget()
    layout = QVBoxLayout(tab)
    
    device_ui = DeviceProfileUI(parent)
    layout.addWidget(device_ui)
    
    return tab, device_ui